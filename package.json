{"name": "nexoria-staff-bot", "version": "1.0.0", "description": "Discord staff management bot with dashboard integration", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'No build step required'", "dashboard": "cd dashboard && npm start", "setup": "node setup.js", "test": "node test.js", "test-db": "node test.js", "logs": "tail -f logs/bot-$(date +%Y-%m-%d).log"}, "keywords": ["discord", "bot", "staff", "management", "dashboard"], "author": "Nexoria Development", "license": "MIT", "dependencies": {"discord.js": "^14.16.2", "mongoose": "^8.0.0", "dotenv": "^16.3.1", "chalk": "^4.1.2", "moment": "^2.29.4", "node-cron": "^3.0.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.1.7"}, "engines": {"node": ">=16.0.0"}}