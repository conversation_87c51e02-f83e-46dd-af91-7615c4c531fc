<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Rank Management - Nexoria Staff Dashboard</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans:400,500,600" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/open-iconic/1.1.1/font/css/open-iconic-bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/gh/hung1001/font-awesome-pro@0ac23ca/css/all.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.9/flatpickr.min.js">
    <link rel="stylesheet" href="../<%= typeof theme !== 'undefined' ? theme.theme : 'home/assets/stylesheets/theme.css' %>" data-skin="default">
    <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
    <style>
        .rank-card {
            transition: transform 0.2s;
            cursor: move;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .rank-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .rank-level {
            font-size: 1.2rem;
            font-weight: bold;
        }
        .permission-badge {
            font-size: 0.7rem;
            margin: 2px;
        }
        .sortable-ghost {
            opacity: 0.4;
        }
        .sortable-chosen {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="app-header app-header-dark">
            <div class="top-bar">
                <div class="top-bar-brand">
                    <h5>🛡️ Nexoria Staff Dashboard</h5>
                </div>
                <div class="top-bar-list">
                    <div class="top-bar-item px-2 d-md-none d-lg-none d-xl-none">
                        <button class="hamburger hamburger-squeeze" type="button" data-toggle="aside" aria-label="toggle menu">
                            <span class="hamburger-box"><span class="hamburger-inner"></span></span>
                        </button>
                    </div>
                    <div class="top-bar-item top-bar-item-right px-0 d-none d-sm-flex">
                        <div class="dropdown d-none d-md-flex">
                            <button class="btn-account d-none d-md-flex" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="user-avatar user-avatar-md">
                                    <img src="https://cdn.discordapp.com/avatars/<%= typeof profile !== 'undefined' ? profile.id : '' %>/<%= typeof profile !== 'undefined' ? profile.avatar : '' %>" alt="">
                                </span>
                                <span class="account-summary pr-lg-4 d-none d-lg-block">
                                    <span class="account-name"><%= typeof profile !== 'undefined' ? profile.username : 'User' %></span>
                                    <span class="account-description">Staff Manager</span>
                                </span>
                            </button>
                            <div class="dropdown-menu">
                                <div class="dropdown-arrow d-lg-none" x-arrow=""></div>
                                <div class="dropdown-arrow ml-3 d-none d-lg-block"></div>
                                <h6 class="dropdown-header d-none d-md-block d-lg-none"><%= typeof profile !== 'undefined' ? profile.username : 'User' %></h6>
                                <a class="dropdown-item" href="/logout">
                                    <span class="dropdown-icon oi oi-account-logout"></span> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <aside class="app-aside app-aside-expand-md app-aside-light">
            <div class="aside-content">
                <header class="aside-header d-block d-md-none">
                    <button class="btn-account" type="button" data-toggle="collapse" data-target="#dropdown-aside">
                        <span class="user-avatar user-avatar-lg">
                            <img src="https://cdn.discordapp.com/avatars/<%= typeof profile !== 'undefined' ? profile.id : '' %>/<%= typeof profile !== 'undefined' ? profile.avatar : '' %>" alt="">
                        </span>
                        <span class="account-icon"><span class="fa fa-caret-down fa-lg"></span></span>
                        <span class="account-summary">
                            <span class="account-name"><%= typeof profile !== 'undefined' ? profile.username : 'User' %></span>
                            <span class="account-description">Staff Manager</span>
                        </span>
                    </button>
                    <div id="dropdown-aside" class="dropdown-aside collapse">
                        <div class="pb-3">
                            <a class="dropdown-item" href="/logout">
                                <span class="dropdown-icon oi oi-account-logout"></span> Logout
                            </a>
                        </div>
                    </div>
                </header>
                <div class="aside-menu overflow-hidden">
                    <nav id="stacked-menu" class="stacked-menu">
                        <ul class="menu">
                            <li class="menu-item">
                                <a href="/home" class="menu-link">
                                    <span class="menu-icon fas fa-home"></span>
                                    <span class="menu-text">Dashboard</span>
                                </a>
                            </li>
                            <li class="menu-item has-active">
                                <a href="/staff" class="menu-link">
                                    <span class="menu-icon fas fa-users"></span>
                                    <span class="menu-text">Staff Management</span>
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="/guilds" class="menu-link">
                                    <span class="menu-icon fas fa-server"></span>
                                    <span class="menu-text">Servers</span>
                                </a>
                            </li>
                            <% if (typeof profile !== 'undefined' && profile.isGlobalAdmin) { %>
                            <li class="menu-item">
                                <a href="/admin" class="menu-link">
                                    <span class="menu-icon fas fa-shield-alt"></span>
                                    <span class="menu-text">Admin Panel</span>
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="/settings" class="menu-link">
                                    <span class="menu-icon fas fa-cog"></span>
                                    <span class="menu-text">Settings</span>
                                </a>
                            </li>
                            <% } %>
                        </ul>
                    </nav>
                </div>
            </div>
        </aside>

        <main class="app-main">
            <div class="wrapper">
                <div class="page">
                    <div class="page-inner">
                        <header class="page-title-bar">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="/home"><i class="breadcrumb-icon fas fa-home mr-2"></i>Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="/staff"><i class="breadcrumb-icon fas fa-users mr-2"></i>Staff Management</a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        <i class="breadcrumb-icon fas fa-layer-group mr-2"></i>Rank Management
                                    </li>
                                </ol>
                            </nav>
                            <h1 class="page-title">Rank Management</h1>
                            <% if (selectedGuild) { %>
                            <p class="text-muted">Managing ranks for <%= selectedGuild.name %></p>
                            <% } %>
                        </header>

                        <div class="page-section">
                            <div class="section-block">
                                <!-- Guild Selector -->
                                <% if (guilds.length > 0) { %>
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="guildSelector" class="form-label">Select Server:</label>
                                        <select class="form-control" id="guildSelector" onchange="changeGuild()">
                                            <% guilds.forEach(guild => { %>
                                            <option value="<%= guild.id %>" <%= selectedGuild && selectedGuild.id === guild.id ? 'selected' : '' %>>
                                                <%= guild.name %>
                                            </option>
                                            <% }); %>
                                        </select>
                                    </div>
                                </div>
                                <% } %>

                                <% if (!selectedGuild) { %>
                                <div class="card">
                                    <div class="card-body text-center py-5">
                                        <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                                        <h4>No Server Selected</h4>
                                        <p class="text-muted">Please select a server to manage ranks.</p>
                                        <a href="/staff" class="btn btn-primary">
                                            <i class="fas fa-arrow-left"></i> Back to Server Selection
                                        </a>
                                    </div>
                                </div>
                                <% } else { %>

                                <!-- Navigation Tabs -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <ul class="nav nav-tabs card-header-tabs">
                                            <li class="nav-item">
                                                <a class="nav-link" href="/staff?guild=<%= selectedGuild.id %>">
                                                    <i class="fas fa-users"></i> Staff Overview
                                                </a>
                                            </li> 
                                            <li class="nav-item">
                                                <a class="nav-link" href="/applications?guild=<%= selectedGuild.id %>">
                                                    <i class="fas fa-file-alt"></i> Applications
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link active" href="/staff/ranks?guild=<%= selectedGuild.id %>">
                                                    <i class="fas fa-layer-group"></i> Rank Management
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Dashboard Settings -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">
                                                    <i class="fas fa-cog"></i> Dashboard Settings
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <form id="dashboardSettingsForm">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label for="dashboardRole">Dashboard Access Role</label>
                                                                <select class="form-control" id="dashboardRole" name="dashboardRoleId">
                                                                    <option value="">Select a role (optional)</option>
                                                                    <% if (guildRoles) { %>
                                                                        <% guildRoles.forEach(role => { %>
                                                                            <option value="<%= role.id %>" <%= guildSettings.dashboardRoleId === role.id ? 'selected' : '' %>>
                                                                                <%= role.name %>
                                                                            </option>
                                                                        <% }); %>
                                                                    <% } %>
                                                                </select>
                                                                <small class="form-text text-muted">
                                                                    Users with this role can access the dashboard
                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label for="defaultStaffRole">Default Staff Role</label>
                                                                <select class="form-control" id="defaultStaffRole" name="defaultStaffRoleId">
                                                                    <option value="">Select a role (optional)</option>
                                                                    <% if (guildRoles) { %>
                                                                        <% guildRoles.forEach(role => { %>
                                                                            <option value="<%= role.id %>" <%= guildSettings.defaultStaffRoleId === role.id ? 'selected' : '' %>>
                                                                                <%= role.name %>
                                                                            </option>
                                                                        <% }); %>
                                                                    <% } %>
                                                                </select>
                                                                <small class="form-text text-muted">
                                                                    Role given to all accepted staff applications
                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label for="suspendedRole">Suspended Staff Role</label>
                                                                <select class="form-control" id="suspendedRole" name="suspendedRoleId">
                                                                    <option value="">Select a role (optional)</option>
                                                                    <% if (guildRoles) { %>
                                                                        <% guildRoles.forEach(role => { %>
                                                                            <option value="<%= role.id %>" <%= guildSettings.suspendedRoleId === role.id ? 'selected' : '' %>>
                                                                                <%= role.name %>
                                                                            </option>
                                                                        <% }); %>
                                                                    <% } %>
                                                                </select>
                                                                <small class="form-text text-muted">
                                                                    Role given to suspended staff members
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <button type="submit" class="btn btn-success">
                                                                <i class="fas fa-save"></i> Save Settings
                                                            </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add New Rank Button -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <button class="btn btn-primary" data-toggle="modal" data-target="#addRankModal">
                                            <i class="fas fa-plus"></i> Add New Rank
                </button>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Drag and drop</strong> ranks to reorder them. Higher positions = higher authority.
                </div>
            </div>
        </div>

        <!-- Ranks List -->
        <div class="row">
            <div class="col-12">
                <% if (ranks.length === 0) { %>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No ranks found. Create your first rank to get started.
                </div>
                <% } else { %>
                <div id="ranksList">
                    <% ranks.forEach((rank, index) => { %>
                    <div class="card rank-card mb-3" data-rank-id="<%= rank._id %>">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-1">
                                    <div class="rank-level text-center" style="color: <%= rank.color %>">
                                        #<%= rank.level %>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <h5 class="mb-1" style="color: <%= rank.color %>">
                                        <i class="fas fa-grip-vertical text-muted me-2"></i>
                                        <%= rank.name %>
                                    </h5>
                                    <small class="text-muted">Role ID: <%= rank.roleId %></small>
                                </div>
                                <div class="col-md-5">
                                    <div class="permissions">
                                        <% if (rank.permissions.canHire) { %>
                                        <span class="badge bg-success permission-badge">Hire</span>
                                        <% } %>
                                        <% if (rank.permissions.canFire) { %>
                                        <span class="badge bg-danger permission-badge">Fire</span>
                                        <% } %>
                                        <% if (rank.permissions.canPromote) { %>
                                        <span class="badge bg-primary permission-badge">Promote</span>
                                        <% } %>
                                        <% if (rank.permissions.canDemote) { %>
                                        <span class="badge bg-warning permission-badge">Demote</span>
                                        <% } %>
                                        <% if (rank.permissions.canSuspend) { %>
                                        <span class="badge bg-dark permission-badge">Suspend</span>
                                        <% } %>
                                        <% if (rank.permissions.canViewLogs) { %>
                                        <span class="badge bg-info permission-badge">View Logs</span>
                                        <% } %>
                                        <% if (rank.permissions.canManageRanks) { %>
                                        <span class="badge bg-secondary permission-badge">Manage Ranks</span>
                                        <% } %>
                                    </div>
                                    <% if (rank.description) { %>
                                    <small class="text-muted d-block mt-1"><%= rank.description %></small>
                                    <% } %>
                                </div>
                                <div class="col-md-3 text-end">
                                    <button class="btn btn-sm btn-outline-primary" onclick="editRank('<%= rank._id %>')">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRank('<%= rank._id %>', '<%= rank.name %>')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <% }); %>
                </div>
                <% } %>
            </div>
        </div>

                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer class="app-footer">
            <ul class="list-inline">
                <li class="list-inline-item">
                    <a class="text-muted" href="#" target="_blank">Support</a>
                </li>
            </ul>
            <div class="copyright">Nexoria Staff Dashboard</div>
        </footer>
    </div>

    <!-- Add Rank Modal -->
    <div class="modal fade" id="addRankModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Rank</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="addRankForm">
                    <div class="modal-body">
                        <input type="hidden" name="guildId" value="<%= selectedGuild ? selectedGuild.id : '' %>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Rank Name</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Discord Role ID</label>
                                    <input type="text" class="form-control" name="roleId" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Color</label>
                                    <input type="color" class="form-control form-control-color" name="color" value="#99AAB5">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <input type="text" class="form-control" name="description" placeholder="Optional description">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Permissions</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canHire]" id="canHire">
                                        <label class="form-check-label" for="canHire">Can Hire Staff</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canFire]" id="canFire">
                                        <label class="form-check-label" for="canFire">Can Fire Staff</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canPromote]" id="canPromote">
                                        <label class="form-check-label" for="canPromote">Can Promote Staff</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canDemote]" id="canDemote">
                                        <label class="form-check-label" for="canDemote">Can Demote Staff</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canSuspend]" id="canSuspend">
                                        <label class="form-check-label" for="canSuspend">Can Suspend Staff</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canViewLogs]" id="canViewLogs">
                                        <label class="form-check-label" for="canViewLogs">Can View Logs</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="permissions[canManageRanks]" id="canManageRanks">
                                        <label class="form-check-label" for="canManageRanks">Can Manage Ranks</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Rank</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="../home/<USER>/vendor/popper-js/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="../home/<USER>/javascript/theme.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        // Initialize sortable
        <% if (selectedGuild && ranks.length > 0) { %>
        const ranksList = document.getElementById('ranksList');
        const sortable = Sortable.create(ranksList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                const rankOrder = Array.from(ranksList.children).map(card => card.dataset.rankId);
                updateRankOrder(rankOrder);
            }
        });
        <% } %>

        function changeGuild() {
            const guildId = document.getElementById('guildSelector').value;
            window.location.href = `/staff/ranks?guild=${guildId}`;
        }

        function updateRankOrder(rankOrder) {
            fetch('/staff/ranks/reorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    guildId: '<%= selectedGuild ? selectedGuild.id : "" %>',
                    rankOrder: rankOrder
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update level numbers in UI
                    const cards = document.querySelectorAll('.rank-card');
                    cards.forEach((card, index) => {
                        const levelElement = card.querySelector('.rank-level');
                        levelElement.textContent = `#${cards.length - index}`;
                    });
                } else {
                    alert('Failed to update rank order: ' + data.error);
                    location.reload(); // Reload to reset order
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating rank order');
                location.reload();
            });
        }

        // Dashboard settings form submission
        document.getElementById('dashboardSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());

            fetch('/staff/settings/dashboard', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    guildId: '<%= selectedGuild.id %>',
                    ...data
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Dashboard settings updated successfully!');
                } else {
                    alert('Error: ' + (data.message || 'Failed to update settings'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating dashboard settings');
            });
        });

        // Add rank form submission
        document.getElementById('addRankForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // Convert permissions to proper format
            data.permissions = {};
            formData.forEach((value, key) => {
                if (key.startsWith('permissions[')) {
                    const permKey = key.match(/permissions\[(.+)\]/)[1];
                    data.permissions[permKey] = 'on';
                }
            });

            fetch('/staff/ranks/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to create rank: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating the rank');
            });
        });

        function editRank(rankId) {
            // TODO: Implement edit functionality
            alert('Edit functionality coming soon!');
        }

        function deleteRank(rankId, rankName) {
            if (confirm(`Are you sure you want to delete the rank "${rankName}"? This action cannot be undone.`)) {
                fetch(`/staff/ranks/delete/${rankId}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to delete rank: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the rank');
                });
            }
        }
    </script>
</body>
</html>
