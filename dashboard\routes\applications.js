const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../auth/auth');
const { StaffApplication, ApplicationForm, StaffRank, StaffMember, GuildSettings } = require('../../models');
const axios = require('axios');
const jsonfile = require('jsonfile');

const themes = __dirname + "/../config/theme.json";

// Applications overview page
router.get('/applications', ensureAuthenticated, async (req, res) => {
    try {
        const selectedGuild = req.query.guild ? req.user.guilds.find(g => g.id === req.query.guild) : null;
        
        if (!selectedGuild) {
            return res.redirect('/home');
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(selectedGuild.id);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(selectedGuild.id);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            req.flash('error', 'Access denied');
            return res.redirect('/home');
        }

        // Get applications and forms
        const applications = await StaffApplication.getByGuild(selectedGuild.id);
        const applicationForms = await ApplicationForm.getByGuild(selectedGuild.id);
        const pendingCount = await StaffApplication.getPendingCount(selectedGuild.id);
        const ranks = await StaffRank.find({ guildId: selectedGuild.id, isActive: true }).sort({ level: -1 });

        var theme = jsonfile.readFileSync(themes);

        res.render('staff/applications', {
            title: 'Staff Applications',
            user: req.user,
            selectedGuild,
            guilds: req.user.guilds,
            applications,
            applicationForms,
            pendingCount,
            ranks,
            theme: theme,
            messages: {
                success: req.flash('success'),
                error: req.flash('error')
            }
        });
    } catch (error) {
        console.error('Error loading applications page:', error);
        req.flash('error', 'Failed to load applications');
        res.redirect('/home');
    }
});

// Get application form data
router.get('/applications/form/:formId', ensureAuthenticated, async (req, res) => {
    try {
        const { formId } = req.params;

        const form = await ApplicationForm.findById(formId).populate('settings.defaultRank');
        if (!form) {
            return res.status(404).json({ error: 'Application form not found' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(form.guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(form.guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        res.json({
            success: true,
            form: {
                ...form.toObject(),
                shareableUrl: form.getShareableUrl()
            }
        });
    } catch (error) {
        console.error('Error getting application form:', error);
        res.status(500).json({ error: 'Failed to get application form' });
    }
});

// Create new application form
router.post('/applications/form/create', ensureAuthenticated, async (req, res) => {
    try {
        const { guildId, title, description, webhookUrl } = req.body;

        if (!guildId || !title) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Deactivate existing forms
        await ApplicationForm.updateMany({ guildId }, { isActive: false });

        // Create new form with default questions
        const applicationForm = new ApplicationForm({
            guildId,
            title,
            description: description || 'Please fill out this application to join our staff team.',
            questions: ApplicationForm.getDefaultQuestions(),
            createdBy: req.user.id,
            createdByUsername: req.user.username,
            isActive: true,
            settings: {
                webhookUrl: webhookUrl || null,
                autoAccept: false,
                defaultRank: null,
                requireDiscordAccount: true,
                allowMultipleApplications: false,
                cooldownHours: 24
            }
        });

        await applicationForm.save();

        res.json({ 
            success: true, 
            message: 'Application form created successfully',
            formId: applicationForm._id,
            shareableUrl: applicationForm.getShareableUrl()
        });
    } catch (error) {
        console.error('Error creating application form:', error);
        res.status(500).json({ error: 'Failed to create application form' });
    }
});

// Update application form
router.post('/applications/form/:formId/update', ensureAuthenticated, async (req, res) => {
    try {
        const { formId } = req.params;
        const { title, description, questions, settings } = req.body;

        const form = await ApplicationForm.findById(formId);
        if (!form) {
            return res.status(404).json({ error: 'Application form not found' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(form.guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(form.guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Update form
        if (title) form.title = title;
        if (description) form.description = description;
        if (questions) form.questions = questions;
        if (settings) form.settings = { ...form.settings, ...settings };

        await form.save();

        res.json({ 
            success: true, 
            message: 'Application form updated successfully',
            shareableUrl: form.getShareableUrl()
        });
    } catch (error) {
        console.error('Error updating application form:', error);
        res.status(500).json({ error: 'Failed to update application form' });
    }
});

// Review application (accept/deny)
router.post('/applications/:applicationId/review', ensureAuthenticated, async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { action, notes, assignedRank } = req.body;

        if (!['accept', 'deny'].includes(action)) {
            return res.status(400).json({ error: 'Invalid action' });
        }

        const application = await StaffApplication.findById(applicationId).populate('applicationFormId');
        if (!application) {
            return res.status(404).json({ error: 'Application not found' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(application.guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(application.guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Update application
        application.status = action === 'accept' ? 'ACCEPTED' : 'DENIED';
        application.reviewedAt = new Date();
        application.reviewedBy = req.user.id;
        application.reviewerUsername = req.user.username;
        application.reviewNotes = notes || null;

        // Handle acceptance - assign roles and rank
        if (action === 'accept') {
            const discord = require('../bot');
            const client = discord.getClient();

            if (client) {
                try {
                    const guild = await client.guilds.fetch(application.guildId);
                    const member = await guild.members.fetch(application.applicantId);

                    // Assign default staff role if configured
                    if (guildSettings.defaultStaffRoleId) {
                        try {
                            await member.roles.add(guildSettings.defaultStaffRoleId);
                            console.log(`Added default staff role to ${application.applicantUsername}`);
                        } catch (roleError) {
                            console.error('Error adding default staff role:', roleError);
                        }
                    }

                    // Assign lowest rank if no specific rank provided
                    let rankToAssign = assignedRank;
                    if (!rankToAssign) {
                        // Get the lowest rank (highest level number)
                        const lowestRank = await StaffRank.findOne({
                            guildId: application.guildId,
                            isActive: true
                        }).sort({ level: -1 });

                        if (lowestRank) {
                            rankToAssign = lowestRank._id;
                            application.assignedRank = rankToAssign;

                            // Add the rank's Discord role
                            try {
                                await member.roles.add(lowestRank.roleId);
                                console.log(`Added rank role ${lowestRank.name} to ${application.applicantUsername}`);
                            } catch (roleError) {
                                console.error('Error adding rank role:', roleError);
                            }
                        }
                    } else {
                        // Add the specifically assigned rank role
                        const assignedRankData = await StaffRank.findById(assignedRank);
                        if (assignedRankData) {
                            try {
                                await member.roles.add(assignedRankData.roleId);
                                console.log(`Added assigned rank role ${assignedRankData.name} to ${application.applicantUsername}`);
                            } catch (roleError) {
                                console.error('Error adding assigned rank role:', roleError);
                            }
                        }
                        application.assignedRank = assignedRank;
                    }

                } catch (discordError) {
                    console.error('Error managing Discord roles:', discordError);
                    // Continue with application acceptance even if role assignment fails
                }
            }

            // Create staff member record if rank was assigned
            if (rankToAssign) {
                try {
                    // Check if staff member already exists
                    const existingStaffMember = await StaffMember.findOne({
                        userId: application.applicantId,
                        guildId: application.guildId
                    });

                    if (!existingStaffMember) {
                        const staffMember = new StaffMember({
                            userId: application.applicantId,
                            username: application.applicantUsername,
                            discriminator: application.applicantDiscriminator,
                            guildId: application.guildId,
                            rankId: rankToAssign,
                            hiredBy: req.user.id,
                            hiredAt: new Date(),
                            isActive: true,
                            isSuspended: false
                        });

                        await staffMember.save();
                        console.log(`Created staff member record for ${application.applicantUsername}`);
                    } else {
                        // Update existing staff member
                        existingStaffMember.rankId = rankToAssign;
                        existingStaffMember.isActive = true;
                        existingStaffMember.isSuspended = false;
                        await existingStaffMember.save();
                        console.log(`Updated existing staff member record for ${application.applicantUsername}`);
                    }
                } catch (staffError) {
                    console.error('Error creating staff member record:', staffError);
                }
            }
        }

        await application.save();

        // Send webhook notification if configured
        if (application.applicationFormId.settings.webhookUrl) {
            try {
                const embed = {
                    title: `📋 Application ${action === 'accept' ? 'Accepted' : 'Denied'}`,
                    color: action === 'accept' ? 0x00ff00 : 0xff0000,
                    fields: [
                        {
                            name: '👤 Applicant',
                            value: `${application.applicantUsername}#${application.applicantDiscriminator}`,
                            inline: true
                        },
                        {
                            name: '👨‍💼 Reviewed By',
                            value: req.user.username,
                            inline: true
                        },
                        {
                            name: '📅 Reviewed',
                            value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                            inline: true
                        }
                    ],
                    timestamp: new Date().toISOString()
                };

                if (notes) {
                    embed.fields.push({
                        name: '📝 Notes',
                        value: notes,
                        inline: false
                    });
                }

                if (action === 'accept' && assignedRank) {
                    const rank = await StaffRank.findById(assignedRank);
                    if (rank) {
                        embed.fields.push({
                            name: '🏆 Assigned Rank',
                            value: rank.name,
                            inline: true
                        });
                    }
                }

                await axios.post(application.applicationFormId.settings.webhookUrl, {
                    embeds: [embed]
                });
            } catch (webhookError) {
                console.error('Error sending webhook:', webhookError);
            }
        }

        res.json({ 
            success: true, 
            message: `Application ${action}ed successfully` 
        });
    } catch (error) {
        console.error('Error reviewing application:', error);
        res.status(500).json({ error: 'Failed to review application' });
    }
});

// Public application form (no auth required)
router.get('/apply/:guildId/:formId', async (req, res) => {
    try {
        const { guildId, formId } = req.params;

        const form = await ApplicationForm.findById(formId).populate('settings.defaultRank');
        if (!form || !form.isActive || form.guildId !== guildId) {
            return res.status(404).render('error_pages/404', {
                message: 'Application form not found or no longer active'
            });
        }

        // Get guild info
        const discord = require('../bot');
        const client = discord.getClient();
        let guild = null;
        if (client) {
            try {
                guild = await client.guilds.fetch(guildId);
            } catch (err) {
                console.log('Could not fetch guild data:', err.message);
            }
        }

        res.render('applications/apply', {
            form,
            guild,
            user: req.user || null
        });
    } catch (error) {
        console.error('Error loading application form:', error);
        res.status(500).render('error_pages/500', {
            message: 'Failed to load application form'
        });
    }
});

// Submit application (no auth required)
router.post('/apply/:guildId/:formId/submit', async (req, res) => {
    try {
        const { guildId, formId } = req.params;
        const { responses, applicantId, applicantUsername, applicantDiscriminator } = req.body;

        const form = await ApplicationForm.findById(formId);
        if (!form || !form.isActive || form.guildId !== guildId) {
            return res.status(404).json({ error: 'Application form not found or no longer active' });
        }

        // Validate required fields
        if (!applicantId || !applicantUsername || !applicantDiscriminator) {
            return res.status(400).json({ error: 'Missing applicant information' });
        }

        // Check for existing application if not allowed
        if (!form.settings.allowMultipleApplications) {
            const existingApp = await StaffApplication.findOne({
                guildId,
                applicantId,
                status: { $in: ['PENDING', 'ACCEPTED'] }
            });

            if (existingApp) {
                return res.status(400).json({
                    error: 'You already have a pending or accepted application for this server'
                });
            }
        }

        // Check cooldown
        if (form.settings.cooldownHours > 0) {
            const cooldownTime = new Date(Date.now() - (form.settings.cooldownHours * 60 * 60 * 1000));
            const recentApp = await StaffApplication.findOne({
                guildId,
                applicantId,
                submittedAt: { $gte: cooldownTime }
            });

            if (recentApp) {
                return res.status(400).json({
                    error: `You must wait ${form.settings.cooldownHours} hours between applications`
                });
            }
        }

        // Validate responses
        const validationErrors = form.validateResponses(responses);
        if (validationErrors.length > 0) {
            return res.status(400).json({ error: validationErrors.join(', ') });
        }

        // Get applicant avatar
        let applicantAvatar = null;
        const discord = require('../bot');
        const client = discord.getClient();
        if (client) {
            try {
                const user = await client.users.fetch(applicantId);
                applicantAvatar = user.avatar;
            } catch (err) {
                console.log('Could not fetch user avatar:', err.message);
            }
        }

        // Create application
        const application = new StaffApplication({
            guildId,
            applicantId,
            applicantUsername,
            applicantDiscriminator,
            applicantAvatar,
            responses: responses.map(r => ({
                questionId: r.questionId,
                question: form.questions.find(q => q.id === r.questionId)?.question || 'Unknown Question',
                answer: r.answer,
                questionType: form.questions.find(q => q.id === r.questionId)?.type || 'text'
            })),
            applicationFormId: formId
        });

        await application.save();

        // Send webhook notification
        if (form.settings.webhookUrl) {
            try {
                const embed = application.toWebhookEmbed();

                // Add quick actions field
                const dashboardUrl = `${req.protocol}://${req.get('host')}/applications?guild=${guildId}`;
                embed.fields.push({
                    name: '🔗 Quick Actions',
                    value: `[📊 View in Dashboard](${dashboardUrl})\n[✅ Accept Application](${dashboardUrl})\n[❌ Deny Application](${dashboardUrl})`,
                    inline: false
                });

                const webhookPayload = {
                    username: 'Nexoria Staff Bot',
                    avatar_url: 'https://cdn.discordapp.com/app-icons/1394486493250191553/icon.png',
                    embeds: [embed]
                };

                await axios.post(form.settings.webhookUrl, webhookPayload);

                application.webhookSent = true;
                await application.save();
            } catch (webhookError) {
                console.error('Error sending webhook:', webhookError);
            }
        }

        res.json({
            success: true,
            message: 'Application submitted successfully! You will be notified of the decision.'
        });
    } catch (error) {
        console.error('Error submitting application:', error);
        res.status(500).json({ error: 'Failed to submit application' });
    }
});

// Delete application form
router.delete('/applications/form/:formId/delete', ensureAuthenticated, async (req, res) => {
    try {
        const { formId } = req.params;

        const form = await ApplicationForm.findById(formId);
        if (!form) {
            return res.status(404).json({ error: 'Application form not found' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(form.guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(form.guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Delete all applications for this form
        await StaffApplication.deleteMany({ applicationFormId: formId });

        // Delete the form
        await ApplicationForm.findByIdAndDelete(formId);

        res.json({
            success: true,
            message: 'Application form and all related applications deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting application form:', error);
        res.status(500).json({ error: 'Failed to delete application form' });
    }
});

// Delete individual application
router.delete('/applications/:applicationId/delete', ensureAuthenticated, async (req, res) => {
    try {
        const { applicationId } = req.params;

        const application = await StaffApplication.findById(applicationId);
        if (!application) {
            return res.status(404).json({ error: 'Application not found' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(application.guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(application.guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Delete the application
        await StaffApplication.findByIdAndDelete(applicationId);

        res.json({
            success: true,
            message: 'Application deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting application:', error);
        res.status(500).json({ error: 'Failed to delete application' });
    }
});

// API endpoint to get application details
router.get('/api/applications/:applicationId', ensureAuthenticated, async (req, res) => {
    try {
        const { applicationId } = req.params;

        const application = await StaffApplication.findById(applicationId)
            .populate('applicationFormId')
            .populate('assignedRank');

        if (!application) {
            return res.status(404).json({ error: 'Application not found' });
        }

        // Check if user has access to this guild
        const userGuild = req.user.guilds.find(g => g.id === application.guildId);
        if (!userGuild) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Check dashboard permissions
        const guildSettings = await GuildSettings.getGuildSettings(application.guildId);
        const discord = require('../bot');
        const client = discord.getClient();
        let member = null;

        if (client) {
            try {
                const guild = await client.guilds.fetch(application.guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            return res.status(403).json({ error: 'Access denied' });
        }

        res.json({
            success: true,
            application: application
        });
    } catch (error) {
        console.error('Error fetching application:', error);
        res.status(500).json({ error: 'Failed to fetch application' });
    }
});

module.exports = router;
