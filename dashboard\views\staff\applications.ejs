<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Staff Applications - Nexoria Staff Dashboard</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans:400,500,600" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/open-iconic/1.1.1/font/css/open-iconic-bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/gh/hung1001/font-awesome-pro@0ac23ca/css/all.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.9/flatpickr.min.js">
    <link rel="stylesheet" href="../<%= typeof theme !== 'undefined' ? theme.theme : 'home/assets/stylesheets/theme.css' %>" data-skin="default">
    <style>
        .application-card {
            transition: transform 0.2s;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .application-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .status-pending {
            color: #fbbf24;
        }
        .status-accepted {
            color: #57f287;
        }
        .status-denied {
            color: #ed4245;
        }
        .form-entry {
            border-left: 3px solid #5865f2;
            padding-left: 10px;
            margin-bottom: 10px;
        }

    </style>
    <style>
        .badge-pending { background-color: #faa61a; }
        .badge-accepted { background-color: #57f287; color: #000; }
        .badge-denied { background-color: #ed4245; }
        .question-item {
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            background: rgba(255,255,255,0.05);
            transition: all 0.2s ease;
        }
        .question-item:hover {
            background: rgba(255,255,255,0.08);
            border-color: rgba(255,255,255,0.2);
        }
        .question-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 15px;
        }
        .drag-handle {
            cursor: move;
            color: #adb5bd;
            font-size: 1.2rem;
        }
        .drag-handle:hover {
            color: #fff;
        }
        .question-preview {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255,255,255,0.03);
            border-radius: 6px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .question-item .form-control {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #fff;
        }
        .question-item .form-control:focus {
            background: rgba(255,255,255,0.15);
            border-color: #5865f2;
            color: #fff;
        }
        .question-item .form-control::placeholder {
            color: #adb5bd;
        }
        .question-item .form-select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #fff;
        }
        .question-item .form-check-input {
            background-color: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.2);
        }
        .question-item .form-check-input:checked {
            background-color: #5865f2;
            border-color: #5865f2;
        }
        .question-item label {
            color: #e9ecef;
            font-weight: 500;
        }
        #questionsContainer {
            min-height: 100px;
            border: 2px dashed rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 20px;
            background: rgba(255,255,255,0.02);
        }
        #questionsContainer:empty::before {
            content: "No questions added yet. Click 'Add Question' to get started.";
            color: #6c757d;
            font-style: italic;
            display: block;
            text-align: center;
            padding: 40px 20px;
        }
        .btn-outline-primary {
            border-color: #5865f2;
            color: #5865f2;
        }
        .btn-outline-primary:hover {
            background-color: #5865f2;
            border-color: #5865f2;
            color: white;
        }
        .btn-outline-danger {
            border-color: #ed4245;
            color: #ed4245;
        }
        .btn-outline-danger:hover {
            background-color: #ed4245;
            border-color: #ed4245;
            color: white;
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="app-header app-header-dark">
            <div class="top-bar">
                <div class="top-bar-brand">
                    <h5>🛡️ Nexoria Staff Dashboard</h5>
                </div>
                <div class="top-bar-list">
                    <div class="top-bar-item px-2 d-md-none d-lg-none d-xl-none">
                        <button class="hamburger hamburger-squeeze" type="button" data-toggle="aside" aria-label="toggle menu">
                            <span class="hamburger-box"><span class="hamburger-inner"></span></span>
                        </button>
                    </div>
                    <div class="top-bar-item top-bar-item-right px-0 d-none d-sm-flex">
                        <ul class="header-nav nav">
                            <li class="nav-item dropdown header-nav-dropdown">
                                <a class="nav-link" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span class="oi oi-grid-three-up"></span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-rich dropdown-menu-right">
                                    <div class="dropdown-arrow"></div>
                                    <div class="dropdown-sheets">
                                        <div class="dropdown-sheet-item">
                                            <a href="/home" class="tile-wrapper">
                                                <span class="tile tile-lg bg-indigo">
                                                    <i class="fas fa-home"></i>
                                                </span>
                                                <span class="tile-peek">Dashboard</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="nav-item dropdown header-nav-dropdown">
                                <a class="nav-link" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span class="oi oi-person"></span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-rich dropdown-menu-right">
                                    <div class="dropdown-arrow"></div>
                                    <h6 class="dropdown-header stop-propagation">
                                        <span class="dropdown-header-title"><%= user.username %></span>
                                    </h6>
                                    <a href="/logout" class="dropdown-item">
                                        <span class="dropdown-icon oi oi-account-logout"></span> Sign out
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <aside class="app-aside app-aside-expand-md app-aside-light">
            <div class="aside-content">
                <header class="aside-header d-block d-md-none">
                    <button class="btn-account" type="button" data-toggle="collapse" data-target="#dropdown-aside">
                        <span class="user-avatar user-avatar-lg">
                            <img src="<%= user.avatar ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png` : '/home/<USER>/images/avatars/profile.jpg' %>" alt="">
                        </span>
                        <span class="account-summary pr-lg-4 d-lg-block">
                            <span class="account-name"><%= user.username %></span>
                            <span class="account-description">Staff Manager</span>
                        </span>
                    </button>
                    <div id="dropdown-aside" class="dropdown-aside collapse">
                        <div class="pb-3">
                            <a class="dropdown-item" href="/logout">
                                <span class="dropdown-icon oi oi-account-logout"></span> Sign out
                            </a>
                        </div>
                    </div>
                </header>
                <div class="aside-menu overflow-hidden">
                    <nav id="stacked-menu" class="stacked-menu">
                        <ul class="menu">
                            <li class="menu-item">
                                <a href="/home" class="menu-link">
                                    <span class="menu-icon fas fa-home"></span>
                                    <span class="menu-text">Dashboard</span>
                                </a>
                            </li>
                            <li class="menu-item has-active">
                                <a href="/staff" class="menu-link">
                                    <span class="menu-icon fas fa-users"></span>
                                    <span class="menu-text">Staff Management</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </aside>

        <main class="app-main">
            <div class="wrapper">
                <div class="page">
                    <div class="page-inner">
                        <header class="page-title-bar">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="/home"><i class="breadcrumb-icon fas fa-home mr-2"></i>Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="/staff"><i class="breadcrumb-icon fas fa-users mr-2"></i>Staff Management</a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        <a href="/applications"><i class="breadcrumb-icon fas fa-file-alt mr-2"></i>Applications</a>
                                    </li>
                                    
                                </ol>
                            </nav>
                            <h1 class="page-title">Staff Applications</h1>
                        </header>
                        <div class="page-section">
                            <!-- Guild Selector -->
                            <% if (guilds && guilds.length > 0) { %>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="guildSelector" class="form-label">Select Server:</label>
                                    <select class="form-control" id="guildSelector" onchange="changeGuild()">
                                        <option value="">Choose a server...</option>
                                        <% guilds.forEach(guild => { %>
                                            <option value="<%= guild.id %>" <%= selectedGuild && selectedGuild.id === guild.id ? 'selected' : '' %>>
                                                <%= guild.name %>
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>
                            </div>
                            <% } %>

                            <% if (selectedGuild) { %>
                            <!-- Application Management Card -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs">
                                        <li class="nav-item">
                                            <a class="nav-link" href="/staff?guild=<%= selectedGuild.id %>">
                                                <i class="fas fa-users"></i> Staff Overview
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link active" href="/applications?guild=<%= selectedGuild.id %>">
                                                <i class="fas fa-file-alt"></i> Applications
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" href="/staff/ranks?guild=<%= selectedGuild.id %>">
                                                <i class="fas fa-layer-group"></i> Rank Management
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <h5 class="card-title mb-1">Staff Applications</h5>
                                            <p class="text-muted mb-0">Manage application forms and review submissions</p>
                                        </div>
                                        <div>
                                            <span class="badge badge-warning mr-2">
                                                <i class="fas fa-clock"></i> <%= pendingCount %> Pending
                                            </span>
                                            <button class="btn btn-primary" onclick="showCreateFormModal()">
                                                <i class="fas fa-plus"></i> Create Application Form
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Application Forms -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-clipboard-list"></i> Application Forms</h5>
                                                </div>
                                                <div class="card-body">
                        <% if (applicationForms && applicationForms.length > 0) { %>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Applications</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% applicationForms.forEach(form => { %>
                                    <tr>
                                        <td><%= form.title %></td>
                                        <td>
                                            <span class="badge <%= form.isActive ? 'bg-success' : 'bg-secondary' %>">
                                                <%= form.isActive ? 'Active' : 'Inactive' %>
                                            </span>
                                        </td>
                                        <td><%= new Date(form.createdAt).toLocaleDateString() %></td>
                                        <td>
                                            <% const formApps = applications.filter(app => app.applicationFormId._id.toString() === form._id.toString()) %>
                                            <%= formApps.length %> total
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editForm('<%= form._id %>')">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" onclick="copyFormShareLink('<%= form.getShareableUrl() %>')">
                                                <i class="fas fa-share"></i> Share
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteForm('<%= form._id %>', '<%= form.title %>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </td>
                                    </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                        <% } else { %>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5>No Application Forms</h5>
                            <p class="text-muted">Create your first application form to start receiving applications.</p>
                            <button class="btn btn-primary" onclick="showCreateFormModal()">
                                <i class="fas fa-plus"></i> Create Application Form
                            </button>
                        </div>
                                                    <% } %>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Applications List -->
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h5><i class="fas fa-inbox"></i> Recent Applications</h5>
                                                    <div>
                                                        <select class="form-control form-control-sm" id="statusFilter" onchange="filterApplications()">
                                                            <option value="">All Status</option>
                                                            <option value="PENDING">Pending</option>
                                                            <option value="ACCEPTED">Accepted</option>
                                                            <option value="DENIED">Denied</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                        <% if (applications && applications.length > 0) { %>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped" id="applicationsTable">
                                <thead>
                                    <tr>
                                        <th>Applicant</th>
                                        <th>Form</th>
                                        <th>Status</th>
                                        <th>Submitted</th>
                                        <th>Reviewed By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% applications.forEach(app => { %>
                                    <tr data-status="<%= app.status %>">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <% if (app.applicantAvatar) { %>
                                                <img src="https://cdn.discordapp.com/avatars/<%= app.applicantId %>/<%= app.applicantAvatar %>.png" 
                                                     class="rounded-circle me-2" width="32" height="32">
                                                <% } else { %>
                                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 32px; height: 32px;">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <% } %>
                                                <div>
                                                    <div><%= app.applicantUsername %>#<%= app.applicantDiscriminator %></div>
                                                    <small class="text-muted"><%= app.applicantId %></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><%= app.applicationFormId.title %></td>
                                        <td>
                                            <span class="badge badge-<%= app.status.toLowerCase() %>">
                                                <%= app.status %>
                                            </span>
                                        </td>
                                        <td>
                                            <div><%= new Date(app.submittedAt).toLocaleDateString() %></div>
                                            <small class="text-muted"><%= new Date(app.submittedAt).toLocaleTimeString() %></small>
                                        </td>
                                        <td>
                                            <% if (app.reviewerUsername) { %>
                                                <%= app.reviewerUsername %>
                                                <br><small class="text-muted"><%= new Date(app.reviewedAt).toLocaleDateString() %></small>
                                            <% } else { %>
                                                <span class="text-muted">Not reviewed</span>
                                            <% } %>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewApplication('<%= app._id %>')">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                            <% if (app.status === 'PENDING') { %>
                                            <button class="btn btn-sm btn-success" onclick="reviewApplication('<%= app._id %>', 'accept')">
                                                <i class="fas fa-check"></i> Accept
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="reviewApplication('<%= app._id %>', 'deny')">
                                                <i class="fas fa-times"></i> Deny
                                            </button>
                                            <% } %>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteApplication('<%= app._id %>', '<%= app.applicantUsername %>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </td>
                                    </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                        <% } else { %>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5>No Applications</h5>
                            <p class="text-muted">No applications have been submitted yet.</p>
                        </div>
                                                    <% } %>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <% } else { %>
                            <div class="text-center py-5">
                                <i class="fas fa-server fa-3x text-muted mb-3"></i>
                                <h3>Select a Server</h3>
                                <p class="text-muted">Choose a server from the dropdown above to manage applications.</p>
                            </div>
                            <% } %>
        </main>

        <footer class="app-footer">
            <ul class="list-inline">
                <li class="list-inline-item">
                    <a class="text-muted" href="#" target="_blank">Support</a>
                </li>
            </ul>
            <div class="copyright">Nexoria Staff Dashboard</div>
        </footer>
    </div>

    <!-- Create Form Modal -->
    <div class="modal fade" id="createFormModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Application Form</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createFormForm">
                        <div class="mb-3">
                            <label for="formTitle" class="form-label">Form Title</label>
                            <input type="text" class="form-control" id="formTitle" placeholder="Staff Application" required>
                        </div>
                        <div class="mb-3">
                            <label for="formDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="formDescription" rows="3" placeholder="Please fill out this application to join our staff team."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="webhookUrl" class="form-label">Discord Webhook URL (Optional)</label>
                            <input type="url" class="form-control" id="webhookUrl" placeholder="https://discord.com/api/webhooks/...">
                            <small class="form-text text-muted">Applications will be logged to this webhook when submitted</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createForm()">Create Form</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form Modal -->
    <div class="modal fade" id="editFormModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Application Form</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editFormForm">
                        <input type="hidden" id="editFormId">

                        <!-- Basic Info -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="editFormTitle" class="form-label">Form Title</label>
                                <input type="text" class="form-control" id="editFormTitle" required>
                            </div>
                            <div class="col-md-6">
                                <label for="editWebhookUrl" class="form-label">Discord Webhook URL</label>
                                <input type="url" class="form-control" id="editWebhookUrl" placeholder="https://discord.com/api/webhooks/...">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="editFormDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="editFormDescription" rows="3"></textarea>
                        </div>

                        <!-- Questions Section -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6>Questions</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addQuestion()">
                                    <i class="fas fa-plus"></i> Add Question
                                </button>
                            </div>
                            <div id="questionsContainer">
                                <!-- Questions will be dynamically added here -->
                            </div>
                        </div>

                        <!-- Share Link -->
                        <div class="mb-3">
                            <label class="form-label">Share Link</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="shareLink" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyShareLink()">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveForm()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Application Modal -->
    <div class="modal fade" id="viewApplicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">View Application</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <img id="viewAppAvatar" src="" alt="Avatar" class="img-fluid rounded-circle" style="width: 64px; height: 64px;">
                        </div>
                        <div class="col-md-10">
                            <h6>Applicant Information</h6>
                            <p><strong>Username:</strong> <span id="viewAppApplicant"></span></p>
                            <p><strong>Application ID:</strong> <code id="viewAppId"></code></p>
                            <p><strong>Submitted:</strong> <span id="viewAppSubmitted"></span></p>
                            <p><strong>Status:</strong> <span id="viewAppStatus" class="badge"></span></p>
                        </div>
                    </div>

                    <hr>

                    <h6>Application Responses</h6>
                    <div id="viewAppResponses">
                        <!-- Responses will be populated here -->
                    </div>

                    <div id="viewAppReviewSection" style="display: none;">
                        <hr>
                        <h6>Review Application</h6>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="viewAppAcceptBtn">
                                <i class="fas fa-check"></i> Accept
                            </button>
                            <button type="button" class="btn btn-danger" id="viewAppDenyBtn">
                                <i class="fas fa-times"></i> Deny
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script>
        // Ensure jQuery is loaded and ready
        $(document).ready(function() {
            console.log('jQuery loaded successfully');
        });

        let currentFormId = null;
        let questionCounter = 0;

        // Add missing tooltip function
        function initializeTooltips() {
            if (typeof $ !== 'undefined' && $.fn.tooltip) {
                $('[data-toggle="tooltip"]').tooltip();
            }
        }

        // Initialize tooltips when document is ready
        $(document).ready(function() {
            initializeTooltips();
        });

        function showCreateFormModal() {
            // Clear any existing values
            document.getElementById('formTitle').value = '';
            document.getElementById('formDescription').value = '';
            document.getElementById('webhookUrl').value = '';

            // Show the modal
            $('#createFormModal').modal('show');
        }

        function changeGuild() {
            const guildId = document.getElementById('guildSelector').value;
            if (guildId) {
                window.location.href = `/applications?guild=${guildId}`;
            }
        }

        function showCreateFormModal() {
            $('#createFormModal').modal('show');
        }

        function createForm() {
            const title = document.getElementById('formTitle').value;
            const description = document.getElementById('formDescription').value;
            const webhookUrl = document.getElementById('webhookUrl').value;

            console.log('Creating form with:', { title, description, webhookUrl });

            if (!title) {
                alert('Please enter a form title');
                return;
            }

            const guildId = '<%= selectedGuild ? selectedGuild.id : "" %>';
            if (!guildId) {
                alert('No guild selected. Please select a server first.');
                return;
            }

            fetch('/applications/form/create', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    guildId: guildId,
                    title: title,
                    description: description,
                    webhookUrl: webhookUrl
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    // Clear form
                    document.getElementById('formTitle').value = '';
                    document.getElementById('formDescription').value = '';
                    document.getElementById('webhookUrl').value = '';

                    // Hide modal
                    $('#createFormModal').modal('hide');

                    alert('Application form created successfully! You can now edit it to add custom questions.');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to create form: ' + error.message);
            });
        }

        function editForm(formId) {
            currentFormId = formId;

            // Fetch form data
            fetch(`/applications/form/${formId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const form = data.form;

                    // Populate basic info
                    document.getElementById('editFormId').value = formId;
                    document.getElementById('editFormTitle').value = form.title;
                    document.getElementById('editFormDescription').value = form.description || '';
                    document.getElementById('editWebhookUrl').value = form.settings?.webhookUrl || '';
                    document.getElementById('shareLink').value = form.shareableUrl;

                    // Clear and populate questions
                    const container = document.getElementById('questionsContainer');
                    container.innerHTML = '';
                    questionCounter = 0;

                    if (form.questions && form.questions.length > 0) {
                        form.questions.sort((a, b) => a.order - b.order).forEach(question => {
                            addQuestionToContainer(question);
                        });
                    } else {
                        // Add default questions if none exist
                        addDefaultQuestions();
                    }

                    // Initialize sortable
                    initializeSortable();

                    $('#editFormModal').modal('show');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to load form data');
            });
        }

        function addDefaultQuestions() {
            const defaultQuestions = [
                { id: 'discord_username', question: 'What is your Discord username?', type: 'text', required: true, placeholder: 'username#1234', order: 1 },
                { id: 'age', question: 'How old are you?', type: 'text', required: true, placeholder: 'Enter your age', order: 2 },
                { id: 'timezone', question: 'What timezone are you in?', type: 'text', required: true, placeholder: 'e.g., EST, PST, GMT', order: 3 },
                { id: 'experience', question: 'Do you have any previous staff experience?', type: 'textarea', required: true, placeholder: 'Describe your previous experience...', order: 4 },
                { id: 'availability', question: 'How many hours per week can you dedicate to staff duties?', type: 'select', required: true, options: ['Less than 5 hours', '5-10 hours', '10-20 hours', '20+ hours'], order: 5 },
                { id: 'why_join', question: 'Why do you want to join our staff team?', type: 'textarea', required: true, placeholder: 'Tell us why you want to be part of our team...', order: 6 }
            ];

            defaultQuestions.forEach(question => {
                addQuestionToContainer(question);
            });
        }

        function copyFormShareLink(url) {
            navigator.clipboard.writeText(url).then(() => {
                alert('Share link copied to clipboard!');
            });
        }

        function copyFormShareLink(url) {
            navigator.clipboard.writeText(url).then(() => {
                alert('Share link copied to clipboard!');
            });
        }

        function copyShareLink() {
            const shareLink = document.getElementById('shareLink');
            shareLink.select();
            shareLink.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(shareLink.value).then(() => {
                alert('Share link copied to clipboard!');
            });
        }

        function filterApplications() {
            const filter = document.getElementById('statusFilter').value;
            const rows = document.querySelectorAll('#applicationsTable tbody tr');

            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                if (!filter || status === filter) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function viewApplication(applicationId) {
            // Fetch application details
            fetch(`/api/applications/${applicationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showApplicationModal(data.application);
                    } else {
                        alert('Error loading application: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to load application details');
                });
        }

        function showApplicationModal(application) {
            // Populate modal with application data
            document.getElementById('viewAppId').textContent = application._id;
            document.getElementById('viewAppApplicant').textContent = `${application.applicantUsername}#${application.applicantDiscriminator}`;
            document.getElementById('viewAppSubmitted').textContent = new Date(application.submittedAt).toLocaleString();
            document.getElementById('viewAppStatus').textContent = application.status;
            document.getElementById('viewAppStatus').className = `badge badge-${application.status.toLowerCase() === 'pending' ? 'warning' : application.status.toLowerCase() === 'accepted' ? 'success' : 'danger'}`;

            // Set applicant avatar
            const avatarUrl = application.applicantAvatar
                ? `https://cdn.discordapp.com/avatars/${application.applicantId}/${application.applicantAvatar}.png`
                : 'https://cdn.discordapp.com/embed/avatars/0.png';
            document.getElementById('viewAppAvatar').src = avatarUrl;

            // Populate responses
            const responsesContainer = document.getElementById('viewAppResponses');
            responsesContainer.innerHTML = '';

            application.responses.forEach((response, index) => {
                const responseDiv = document.createElement('div');
                responseDiv.className = 'mb-3 p-3 border rounded';
                responseDiv.innerHTML = `
                    <h6 class="text-primary">${index + 1}. ${response.question}</h6>
                    <p class="mb-0">${response.answer || 'No response provided'}</p>
                `;
                responsesContainer.appendChild(responseDiv);
            });

            // Show review section if pending
            const reviewSection = document.getElementById('viewAppReviewSection');
            if (application.status === 'PENDING') {
                reviewSection.style.display = 'block';
                document.getElementById('viewAppAcceptBtn').onclick = () => reviewApplication(application._id, 'accept');
                document.getElementById('viewAppDenyBtn').onclick = () => reviewApplication(application._id, 'deny');
            } else {
                reviewSection.style.display = 'none';
            }

            // Show modal
            $('#viewApplicationModal').modal('show');
        }

        function reviewApplication(applicationId, action) {
            const notes = prompt(`${action === 'accept' ? 'Accept' : 'Deny'} this application. Add notes (optional):`);
            if (notes === null) return; // User cancelled

            fetch(`/applications/${applicationId}/review`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    action,
                    notes: notes || null
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal if open
                    $('#viewApplicationModal').modal('hide');

                    // Show success message
                    alert(data.message || `Application ${action}ed successfully!`);

                    // Reload page to show updated status
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to review application');
            });
        }

        function deleteForm(formId, formTitle) {
            if (confirm(`Are you sure you want to delete the form "${formTitle}"? This will also delete ALL applications submitted to this form. This action cannot be undone.`)) {
                fetch(`/applications/form/${formId}/delete`, {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to delete form');
                });
            }
        }

        function deleteApplication(applicationId, applicantUsername) {
            if (confirm(`Are you sure you want to delete the application from ${applicantUsername}? This action cannot be undone.`)) {
                fetch(`/applications/${applicationId}/delete`, {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to delete application');
                });
            }
        }

        function addQuestion() {
            const newQuestion = {
                id: `question_${Date.now()}`,
                question: 'New Question',
                type: 'text',
                required: true,
                placeholder: '',
                options: [],
                order: questionCounter++
            };
            addQuestionToContainer(newQuestion);
            initializeSortable();
        }

        function addQuestionToContainer(question) {
            const container = document.getElementById('questionsContainer');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-item';
            questionDiv.setAttribute('data-question-id', question.id);

            questionDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="drag-handle">
                        <i class="fas fa-grip-vertical"></i>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeQuestion('${question.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="row mb-2">
                    <div class="col-md-8">
                        <label class="form-label">Question Text</label>
                        <input type="text" class="form-control question-text" value="${question.question}" onchange="updateQuestionPreview('${question.id}')">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Type</label>
                        <select class="form-select question-type" onchange="updateQuestionType('${question.id}')">
                            <option value="text" ${question.type === 'text' ? 'selected' : ''}>Text Input</option>
                            <option value="textarea" ${question.type === 'textarea' ? 'selected' : ''}>Long Text</option>
                            <option value="select" ${question.type === 'select' ? 'selected' : ''}>Dropdown</option>
                            <option value="radio" ${question.type === 'radio' ? 'selected' : ''}>Multiple Choice</option>
                            <option value="checkbox" ${question.type === 'checkbox' ? 'selected' : ''}>Checkboxes</option>
                        </select>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-md-6">
                        <label class="form-label">Placeholder Text</label>
                        <input type="text" class="form-control question-placeholder" value="${question.placeholder || ''}" onchange="updateQuestionPreview('${question.id}')">
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mt-4">
                            <input class="form-check-input question-required" type="checkbox" ${question.required ? 'checked' : ''} onchange="updateQuestionPreview('${question.id}')">
                            <label class="form-check-label">Required</label>
                        </div>
                    </div>
                </div>

                <div class="question-options" style="display: ${['select', 'radio', 'checkbox'].includes(question.type) ? 'block' : 'none'}">
                    <label class="form-label">Options (one per line)</label>
                    <textarea class="form-control question-options-text" rows="3" onchange="updateQuestionPreview('${question.id}')">${(question.options || []).join('\\n')}</textarea>
                </div>

                <div class="question-preview">
                    <label class="form-label">Preview:</label>
                    <div class="preview-content"></div>
                </div>
            `;

            container.appendChild(questionDiv);
            updateQuestionPreview(question.id);
        }

        function removeQuestion(questionId) {
            if (confirm('Are you sure you want to remove this question?')) {
                const questionDiv = document.querySelector(`[data-question-id="${questionId}"]`);
                if (questionDiv) {
                    questionDiv.remove();
                }
            }
        }

        function updateQuestionType(questionId) {
            const questionDiv = document.querySelector(`[data-question-id="${questionId}"]`);
            const typeSelect = questionDiv.querySelector('.question-type');
            const optionsDiv = questionDiv.querySelector('.question-options');

            const showOptions = ['select', 'radio', 'checkbox'].includes(typeSelect.value);
            optionsDiv.style.display = showOptions ? 'block' : 'none';

            updateQuestionPreview(questionId);
        }

        function updateQuestionPreview(questionId) {
            const questionDiv = document.querySelector(`[data-question-id="${questionId}"]`);
            const questionText = questionDiv.querySelector('.question-text').value;
            const questionType = questionDiv.querySelector('.question-type').value;
            const placeholder = questionDiv.querySelector('.question-placeholder').value;
            const required = questionDiv.querySelector('.question-required').checked;
            const optionsText = questionDiv.querySelector('.question-options-text').value;
            const options = optionsText.split('\\n').filter(opt => opt.trim());

            const previewContent = questionDiv.querySelector('.preview-content');
            let previewHTML = `<label class="form-label">${questionText}${required ? ' <span class="text-danger">*</span>' : ''}</label>`;

            switch (questionType) {
                case 'text':
                    previewHTML += `<input type="text" class="form-control" placeholder="${placeholder}" disabled>`;
                    break;
                case 'textarea':
                    previewHTML += `<textarea class="form-control" rows="3" placeholder="${placeholder}" disabled></textarea>`;
                    break;
                case 'select':
                    previewHTML += `<select class="form-control" disabled><option>Choose an option...</option>`;
                    options.forEach(opt => previewHTML += `<option>${opt}</option>`);
                    previewHTML += `</select>`;
                    break;
                case 'radio':
                    options.forEach(opt => {
                        previewHTML += `<div class="form-check"><input class="form-check-input" type="radio" disabled><label class="form-check-label">${opt}</label></div>`;
                    });
                    break;
                case 'checkbox':
                    options.forEach(opt => {
                        previewHTML += `<div class="form-check"><input class="form-check-input" type="checkbox" disabled><label class="form-check-label">${opt}</label></div>`;
                    });
                    break;
            }

            previewContent.innerHTML = previewHTML;
        }

        function initializeSortable() {
            const container = document.getElementById('questionsContainer');
            if (container) {
                new Sortable(container, {
                    handle: '.drag-handle',
                    animation: 150,
                    onEnd: function() {
                        // Update order numbers
                        const questions = container.querySelectorAll('.question-item');
                        questions.forEach((question, index) => {
                            question.setAttribute('data-order', index);
                        });
                    }
                });
            }
        }

        function saveForm() {
            const formId = document.getElementById('editFormId').value;
            const title = document.getElementById('editFormTitle').value;
            const description = document.getElementById('editFormDescription').value;
            const webhookUrl = document.getElementById('editWebhookUrl').value;

            // Collect questions
            const questions = [];
            const questionDivs = document.querySelectorAll('.question-item');

            questionDivs.forEach((questionDiv, index) => {
                const questionId = questionDiv.getAttribute('data-question-id');
                const questionText = questionDiv.querySelector('.question-text').value;
                const questionType = questionDiv.querySelector('.question-type').value;
                const placeholder = questionDiv.querySelector('.question-placeholder').value;
                const required = questionDiv.querySelector('.question-required').checked;
                const optionsText = questionDiv.querySelector('.question-options-text').value;
                const options = optionsText.split('\\n').filter(opt => opt.trim());

                questions.push({
                    id: questionId,
                    question: questionText,
                    type: questionType,
                    required: required,
                    placeholder: placeholder,
                    options: options,
                    order: index
                });
            });

            fetch(`/applications/form/${formId}/update`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    title,
                    description,
                    questions,
                    settings: {
                        webhookUrl
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#editFormModal').modal('hide');
                    alert('Form updated successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to save form');
            });
        }
    </script>
</body>
</html>
