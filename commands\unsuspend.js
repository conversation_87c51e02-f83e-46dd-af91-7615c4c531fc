const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const { StaffMember, GuildSettings } = require('../models');
const StaffUtils = require('../utils/staffUtils');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('unsuspend')
        .setDescription('Unsuspend a staff member')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The staff member to unsuspend')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for unsuspension')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction) {
        try {
            await interaction.deferReply();

            const targetUser = interaction.options.getUser('user');
            const reason = interaction.options.getString('reason') || 'Manual unsuspension';
            const executor = interaction.user;
            const guild = interaction.guild;

            // Check if executor has permission to suspend (same permission for unsuspend)
            const hasPermission = await StaffUtils.hasPermission(interaction.member, 'suspend');
            if (!hasPermission) {
                return await interaction.editReply({
                    embeds: [StaffUtils.createErrorEmbed(
                        'Permission Denied',
                        'You do not have permission to unsuspend staff members.'
                    )]
                });
            }

            // Get target member
            const targetMember = await guild.members.fetch(targetUser.id).catch(() => null);
            if (!targetMember) {
                return await interaction.editReply({
                    embeds: [StaffUtils.createErrorEmbed(
                        'User Not Found',
                        'The specified user is not a member of this server.'
                    )]
                });
            }

            // Get staff member record (don't require isActive since suspended members might be marked inactive)
            const staffMember = await StaffMember.findOne({
                userId: targetUser.id,
                guildId: guild.id
            }).populate('rankId');

            if (!staffMember) {
                return await interaction.editReply({
                    embeds: [StaffUtils.createErrorEmbed(
                        'Not Staff',
                        `${targetUser.username} is not an active staff member.`
                    )]
                });
            }

            // Check if actually suspended
            if (!staffMember.isSuspended) {
                return await interaction.editReply({
                    embeds: [StaffUtils.createErrorEmbed(
                        'Not Suspended',
                        `${targetUser.username} is not currently suspended.`
                    )]
                });
            }

            const rank = staffMember.rankId;

            // Get guild settings and remove suspended role if configured
            const guildSettings = await GuildSettings.getGuildSettings(guild.id, guild.name);
            if (guildSettings.suspendedRoleId) {
                try {
                    await targetMember.roles.remove(guildSettings.suspendedRoleId);
                } catch (error) {
                    console.warn('Failed to remove suspended role:', error);
                }
            }

            // Restore previous roles
            if (staffMember.previousRoles && staffMember.previousRoles.length > 0) {
                try {
                    await targetMember.roles.add(staffMember.previousRoles);
                } catch (error) {
                    console.warn('Failed to restore some roles:', error);
                    // Fallback: add basic roles
                    if (rank.roleId) {
                        try {
                            await targetMember.roles.add(rank.roleId);
                        } catch (roleError) {
                            console.warn('Failed to add rank role:', roleError);
                        }
                    }
                    if (guildSettings.defaultStaffRoleId) {
                        try {
                            await targetMember.roles.add(guildSettings.defaultStaffRoleId);
                        } catch (roleError) {
                            console.warn('Failed to add default staff role:', roleError);
                        }
                    }
                }
            } else {
                // Fallback: add rank role and default staff role
                if (rank.roleId) {
                    try {
                        await targetMember.roles.add(rank.roleId);
                    } catch (roleError) {
                        console.warn('Failed to add rank role:', roleError);
                    }
                }
                if (guildSettings.defaultStaffRoleId) {
                    try {
                        await targetMember.roles.add(guildSettings.defaultStaffRoleId);
                    } catch (roleError) {
                        console.warn('Failed to add default staff role:', roleError);
                    }
                }
            }

            // Update staff member record
            staffMember.isSuspended = false;
            staffMember.suspensionEnd = null;
            staffMember.suspendedBy = null;
            staffMember.suspensionReason = null;
            staffMember.previousRoles = [];
            await staffMember.save();

            // Log the action
            await StaffUtils.logAction(
                'UNSUSPEND',
                executor,
                targetUser,
                {
                    reason,
                    rank: {
                        id: rank._id.toString(),
                        name: rank.name,
                        level: rank.level
                    }
                },
                guild.id,
                {
                    channelId: interaction.channel.id,
                    commandUsed: 'unsuspend'
                }
            );

            // Create success embed
            const successEmbed = StaffUtils.createSuccessEmbed(
                'Staff Member Unsuspended',
                `Successfully unsuspended ${targetUser.username}`,
                [
                    { name: 'User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Rank', value: rank.name, inline: true },
                    { name: 'Unsuspended By', value: `<@${executor.id}>`, inline: true },
                    { name: 'Reason', value: reason, inline: false }
                ]
            );

            await interaction.editReply({ embeds: [successEmbed] });

            // Send log to staff log channel
            const logEmbed = StaffUtils.createInfoEmbed(
                'Staff Member Unsuspended',
                `${executor.username} unsuspended ${targetUser.username}`,
                [
                    { name: 'Unsuspended Staff Member', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Rank', value: rank.name, inline: true },
                    { name: 'Unsuspended By', value: `<@${executor.id}>`, inline: true },
                    { name: 'Reason', value: reason, inline: false }
                ]
            );

            await StaffUtils.sendLogToChannel(interaction.client, logEmbed);

        } catch (error) {
            console.error('Error in unsuspend command:', error);
            
            const errorEmbed = StaffUtils.createErrorEmbed(
                'Command Error',
                'An error occurred while processing the unsuspend command. Please try again.'
            );

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
