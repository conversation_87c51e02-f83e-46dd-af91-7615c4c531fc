// Load environment variables from appropriate .env file
const path = require('path');
const fs = require('fs');

// Try to load .env.production first (for deployment), then fallback to .env
const productionEnvPath = path.join(__dirname, '.env.production');
const devEnvPath = path.join(__dirname, '.env');

if (fs.existsSync(productionEnvPath)) {
    console.log('Loading .env.production file...');
    require('dotenv').config({ path: productionEnvPath });
} else if (fs.existsSync(devEnvPath)) {
    console.log('Loading .env file...');
    require('dotenv').config({ path: devEnvPath });
} else {
    console.log('No .env file found, using environment variables...');
}
const { Client, GatewayIntentBits, Collection } = require('discord.js');
const mongoose = require('mongoose');
const chalk = require('chalk');
const { logger } = require('./utils/logger');
const DashboardServer = require('./dashboard/index');
const SuspensionManager = require('./utils/suspensionManager');

// Create Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Collections for commands and cooldowns
client.commands = new Collection();
client.cooldowns = new Collection();

// Initialize suspension manager
let suspensionManager;

// Load command files
const commandsPath = path.join(__dirname, 'commands');
if (fs.existsSync(commandsPath)) {
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            logger.info(`Loaded command: ${command.data.name}`);
        } else {
            logger.warn(`Command at ${filePath} is missing required "data" or "execute" property.`);
        }
    }
}

// Load event files
const eventsPath = path.join(__dirname, 'events');
if (fs.existsSync(eventsPath)) {
    const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));
    
    for (const file of eventFiles) {
        const filePath = path.join(eventsPath, file);
        const event = require(filePath);
        
        if (event.once) {
            client.once(event.name, (...args) => event.execute(...args));
        } else {
            client.on(event.name, (...args) => event.execute(...args));
        }
        logger.info(`Loaded event: ${event.name}`);
    }
}

// Connect to MongoDB
async function connectDatabase() {
    try {
        const mongoUri = process.env.MONGODB_URI;
        console.log('MongoDB URI found:', mongoUri ? 'Yes' : 'No');
        console.log('NODE_ENV:', process.env.NODE_ENV);

        if (!mongoUri) {
            throw new Error('MONGODB_URI environment variable is not set');
        }

        await mongoose.connect(mongoUri, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        logger.info(chalk.green('✅ Connected to MongoDB'));
    } catch (error) {
        logger.error(chalk.red('❌ Failed to connect to MongoDB:'), error);
        process.exit(1);
    }
}

// Initialize dashboard server
let dashboardServer;
function initializeDashboard() {
    try {
        dashboardServer = new DashboardServer(client);
        dashboardServer.start();
        logger.info(chalk.blue('🌐 Dashboard server initialized'));
    } catch (error) {
        logger.error(chalk.red('❌ Failed to initialize dashboard:'), error);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    logger.info(chalk.yellow('🔄 Shutting down bot...'));

    // Stop suspension manager
    if (suspensionManager) {
        suspensionManager.stop();
    }

    if (client.user) {
        await client.destroy();
    }

    if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
    }

    logger.info(chalk.green('✅ Bot shutdown complete'));
    process.exit(0);
});

// Error handling
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

// Start the bot
async function startBot() {
    try {
        logger.info(chalk.cyan('🚀 Starting Nexoria Staff Bot...'));
        
        // Connect to database first
        await connectDatabase();
        
        // Login to Discord
        await client.login(process.env.DISCORD_TOKEN);
        
        // Initialize dashboard and suspension manager after bot is ready
        client.once('ready', () => {
            initializeDashboard();

            // Initialize suspension manager
            suspensionManager = new SuspensionManager(client);
            suspensionManager.start();
        });
        
    } catch (error) {
        logger.error(chalk.red('❌ Failed to start bot:'), error);
        process.exit(1);
    }
}

startBot();

module.exports = client;
