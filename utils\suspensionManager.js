const cron = require('node-cron');
const { StaffMember, GuildSettings } = require('../models');
const StaffUtils = require('./staffUtils');
const { logger } = require('./logger');

class SuspensionManager {
    constructor(client) {
        this.client = client;
        this.isRunning = false;
    }

    /**
     * Start the suspension manager
     */
    start() {
        if (this.isRunning) {
            logger.warn('Suspension manager is already running');
            return;
        }

        // Run every minute to check for expired suspensions
        this.cronJob = cron.schedule('* * * * *', async () => {
            await this.checkExpiredSuspensions();
        }, {
            scheduled: false
        });

        this.cronJob.start();
        this.isRunning = true;
        logger.info('Suspension manager started - checking for expired suspensions every minute');

        // Run initial check
        this.checkExpiredSuspensions();
    }

    /**
     * Stop the suspension manager
     */
    stop() {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob = null;
        }
        this.isRunning = false;
        logger.info('Suspension manager stopped');
    }

    /**
     * Check for and process expired suspensions
     */
    async checkExpiredSuspensions() {
        try {
            const now = new Date();
            
            // Find all suspended staff members whose suspension has expired
            const expiredSuspensions = await StaffMember.find({
                isSuspended: true,
                suspensionEnd: { $lte: now },
                isActive: true
            }).populate('rankId');

            if (expiredSuspensions.length === 0) {
                return; // No expired suspensions
            }

            logger.info(`Found ${expiredSuspensions.length} expired suspension(s) to process`);

            for (const staffMember of expiredSuspensions) {
                await this.unsuspendMember(staffMember);
            }

        } catch (error) {
            logger.error('Error checking expired suspensions:', error);
        }
    }

    /**
     * Automatically unsuspend a staff member
     */
    async unsuspendMember(staffMember) {
        try {
            const guild = await this.client.guilds.fetch(staffMember.guildId);
            if (!guild) {
                logger.warn(`Guild ${staffMember.guildId} not found for staff member ${staffMember.userId}`);
                return;
            }

            const member = await guild.members.fetch(staffMember.userId).catch(() => null);
            if (!member) {
                logger.warn(`Member ${staffMember.userId} not found in guild ${staffMember.guildId}`);
                // Still update the database record
                await this.updateSuspensionRecord(staffMember, 'Member not found in guild');
                return;
            }

            const rank = staffMember.rankId;

            // Get guild settings for suspended role
            const guildSettings = await GuildSettings.getGuildSettings(staffMember.guildId);

            // Remove suspended role if configured
            if (guildSettings.suspendedRoleId) {
                try {
                    await member.roles.remove(guildSettings.suspendedRoleId);
                    logger.info(`Removed suspended role from ${staffMember.username}#${staffMember.discriminator}`);
                } catch (error) {
                    logger.warn(`Failed to remove suspended role from ${staffMember.username}#${staffMember.discriminator}:`, error);
                }
            }

            // Restore previous roles
            if (staffMember.previousRoles && staffMember.previousRoles.length > 0) {
                try {
                    // Filter out roles that no longer exist
                    const validRoles = [];
                    for (const roleId of staffMember.previousRoles) {
                        const role = guild.roles.cache.get(roleId);
                        if (role) {
                            validRoles.push(roleId);
                        }
                    }

                    if (validRoles.length > 0) {
                        await member.roles.add(validRoles);
                        logger.info(`Restored ${validRoles.length} role(s) to ${staffMember.username}#${staffMember.discriminator}`);
                    }
                } catch (error) {
                    logger.warn(`Failed to restore some roles to ${staffMember.username}#${staffMember.discriminator}:`, error);
                    // Fallback: just add their staff role back
                    await StaffUtils.applyStaffRole(member, rank._id);
                }
            } else {
                // Fallback: just add their staff role back
                await StaffUtils.applyStaffRole(member, rank._id);
                logger.info(`Applied staff role to ${staffMember.username}#${staffMember.discriminator}`);
            }

            // Store original suspension end time before updating record
            const originalSuspensionEnd = staffMember.suspensionEnd;

            // Update database record
            await this.updateSuspensionRecord(staffMember, 'Automatic unsuspension - suspension period expired');

            // Log the action
            await StaffUtils.logAction(
                'UNSUSPEND',
                { id: 'SYSTEM', username: 'System', discriminator: '0000' },
                { id: staffMember.userId, username: staffMember.username, discriminator: staffMember.discriminator },
                {
                    reason: 'Automatic unsuspension - suspension period expired',
                    rank: {
                        id: rank._id.toString(),
                        name: rank.name,
                        level: rank.level
                    }
                },
                staffMember.guildId,
                {
                    automated: true
                }
            );

            // Send notification to staff log channel
            const logEmbed = StaffUtils.createInfoEmbed(
                'Automatic Unsuspension',
                `${staffMember.username}#${staffMember.discriminator} has been automatically unsuspended`,
                [
                    { name: 'Staff Member', value: `<@${staffMember.userId}>`, inline: true },
                    { name: 'Rank', value: rank.name, inline: true },
                    { name: 'Reason', value: 'Suspension period expired', inline: true },
                    { name: 'Original Suspension End', value: originalSuspensionEnd ? `<t:${Math.floor(originalSuspensionEnd.getTime() / 1000)}:F>` : 'Unknown', inline: false }
                ]
            );

            await StaffUtils.sendLogToChannel(this.client, logEmbed);

            logger.info(`Successfully unsuspended ${staffMember.username}#${staffMember.discriminator} (${staffMember.userId})`);

        } catch (error) {
            logger.error(`Error unsuspending ${staffMember.username}#${staffMember.discriminator}:`, error);
        }
    }

    /**
     * Update the suspension record in the database
     */
    async updateSuspensionRecord(staffMember, reason) {
        try {
            staffMember.isSuspended = false;
            staffMember.suspensionEnd = null;
            staffMember.suspendedBy = null;
            staffMember.suspensionReason = null;
            staffMember.previousRoles = [];
            await staffMember.save();

            logger.info(`Updated suspension record for ${staffMember.username}#${staffMember.discriminator}: ${reason}`);
        } catch (error) {
            logger.error(`Failed to update suspension record for ${staffMember.username}#${staffMember.discriminator}:`, error);
        }
    }

    /**
     * Get suspension statistics
     */
    async getSuspensionStats() {
        try {
            const totalSuspended = await StaffMember.countDocuments({
                isSuspended: true,
                isActive: true
            });

            const expiringSoon = await StaffMember.countDocuments({
                isSuspended: true,
                isActive: true,
                suspensionEnd: {
                    $lte: new Date(Date.now() + 24 * 60 * 60 * 1000) // Next 24 hours
                }
            });

            return {
                totalSuspended,
                expiringSoon
            };
        } catch (error) {
            logger.error('Error getting suspension stats:', error);
            return {
                totalSuspended: 0,
                expiringSoon: 0
            };
        }
    }

    /**
     * Manually trigger suspension check (for testing or immediate processing)
     */
    async triggerCheck() {
        logger.info('Manually triggering suspension check');
        await this.checkExpiredSuspensions();
    }
}

module.exports = SuspensionManager;
