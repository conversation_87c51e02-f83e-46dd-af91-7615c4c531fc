const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureGuildAccess } = require('../auth/auth');
const { StaffRank, StaffMember, StaffLog, GuildSettings } = require('../../models');
const discord = require('../bot');
const jsonfile = require('jsonfile');
const themes = __dirname + "/../config/theme.json";

// Staff management main page
router.get('/staff', ensureAuthenticated, async (req, res) => {
    try {
        const guildId = req.query.guild;
        if (!guildId) {
            // Show guild selection page with pagination
            const guilds = req.user.guilds || [];
            const client = discord.getClient();
            const botGuilds = client ? client.guilds.cache : new Map();

            console.log(`User has ${guilds.length} guilds, bot is in ${botGuilds.size} guilds`);
            console.log('<PERSON><PERSON> is present in:', Array.from(botGuilds.values()).map(g => `${g.name} (${g.id})`));
            console.log('User guilds include:', guilds.slice(0, 5).map(g => `${g.name} (${g.id})`));

            // First, sort ALL guilds to put bot-present ones first

            // Pre-sort all guilds to ensure bot-present guilds are at the top
            guilds.sort((a, b) => {
                const aBotPresent = botGuilds.has(a.id);
                const bBotPresent = botGuilds.has(b.id);

                if (aBotPresent && !bBotPresent) return -1;
                if (!aBotPresent && bBotPresent) return 1;
                return a.name.localeCompare(b.name);
            });

            // Pagination setup AFTER sorting
            const page = parseInt(req.query.page) || 1;
            const perPage = 10;
            const totalGuilds = guilds.length;
            const totalPages = Math.ceil(totalGuilds / perPage);
            const startIndex = (page - 1) * perPage;
            const endIndex = startIndex + perPage;
            const guildsToShow = guilds.slice(startIndex, endIndex);

            // Process all guilds to show (not just accessible ones)
            const guildPromises = guildsToShow.map(async (guild) => {
                try {
                    const botPresent = botGuilds.has(guild.id);
                    let guildSettings = null;
                    let canAccess = false;

                    console.log(`Guild ${guild.name} (${guild.id}): Bot present = ${botPresent}`);

                    // Special check for the testing server
                    if (guild.name.includes('Nexoria Development') || guild.name.includes('Testing')) {
                        console.log(`*** FOUND TESTING SERVER: ${guild.name} (${guild.id}) - Bot present: ${botPresent} ***`);
                        console.log('Bot guilds:', Array.from(botGuilds.keys()));
                    }

                    // Check settings and access regardless of bot presence
                    try {
                        guildSettings = await GuildSettings.getGuildSettings(guild.id, guild.name);
                        canAccess = guildSettings.canAccessDashboard(req.user.id);
                    } catch (error) {
                        console.log(`No settings found for guild ${guild.name}, creating default`);
                        guildSettings = { isSetup: false };
                        canAccess = false;
                    }

                    return {
                        ...guild,
                        settings: guildSettings,
                        botPresent: botPresent,
                        canAccess: canAccess
                    };
                } catch (error) {
                    console.error(`Error processing guild ${guild.id}:`, error);
                    return {
                        ...guild,
                        settings: { isSetup: false },
                        botPresent: false,
                        canAccess: false
                    };
                }
            });

            const results = await Promise.all(guildPromises);

            // Sort: bot present AND accessible guilds first, then bot present, then others
            results.sort((a, b) => {
                // Priority 1: Bot present AND accessible
                if (a.botPresent && a.canAccess && !(b.botPresent && b.canAccess)) return -1;
                if (!(a.botPresent && a.canAccess) && b.botPresent && b.canAccess) return 1;

                // Priority 2: Bot present (but not accessible)
                if (a.botPresent && !b.botPresent) return -1;
                if (!a.botPresent && b.botPresent) return 1;

                // Priority 3: Alphabetical within same category
                return a.name.localeCompare(b.name);
            });

            console.log('First 3 sorted results:', results.slice(0, 3).map(g => `${g.name} - Bot: ${g.botPresent}, Access: ${g.canAccess}`));

            var theme = jsonfile.readFileSync(themes);
            return res.render('staff/guild-select', {
                title: 'Select Server',
                guilds: results,
                theme: theme,
                profile: req.user,
                pagination: {
                    currentPage: page,
                    totalPages: totalPages,
                    totalGuilds: totalGuilds,
                    hasNext: page < totalPages,
                    hasPrev: page > 1,
                    nextPage: page + 1,
                    prevPage: page - 1
                }
            });
        }

        // Check guild access
        const guildSettings = await GuildSettings.getGuildSettings(guildId);
        if (!guildSettings.canAccessDashboard(req.user.id)) {
            req.flash('error', 'You do not have permission to access staff management for this server.');
            return res.redirect('/home');
        }

        // Get user's accessible guilds (optimized)
        const guilds = req.user.guilds || [];
        const selectedGuildId = guildId;
        const client = discord.getClient();
        const botGuilds = client ? client.guilds.cache : new Map();

        // Only process guilds where bot is present for faster loading
        const accessibleGuilds = [];
        const guildPromises = guilds.filter(g => botGuilds.has(g.id)).slice(0, 10).map(async (guild) => {
            try {
                const settings = await GuildSettings.getGuildSettings(guild.id, guild.name);
                if (settings.canAccessDashboard(req.user.id)) {
                    return {
                        ...guild,
                        settings,
                        botPresent: true
                    };
                }
            } catch (error) {
                console.error(`Error checking access for guild ${guild.id}:`, error);
            }
            return null;
        });

        const results = await Promise.all(guildPromises);
        accessibleGuilds.push(...results.filter(guild => guild !== null));

        // Get ranks for selected guild
        const ranks = await StaffRank.getGuildRanks(selectedGuildId);

        // Get staff members for selected guild
        const staffMembers = await StaffMember.find({
            guildId: selectedGuildId,
            isActive: true
        }).populate('rankId').sort({ 'rankId.level': -1 });

        // Get recent logs
        const recentLogs = await StaffLog.getRecentLogs(selectedGuildId, 20);

        const selectedGuild = accessibleGuilds.find(g => g.id === selectedGuildId);
        var theme = jsonfile.readFileSync(themes);

        res.render('staff/index', {
            title: 'Staff Management',
            guilds: accessibleGuilds,
            selectedGuild,
            ranks,
            staffMembers,
            recentLogs,
            theme: theme,
            profile: req.user
        });
    } catch (error) {
        console.error('Error loading staff page:', error);
        req.flash('error', 'An error occurred while loading the staff management page.');
        res.redirect('/home');
    }
});

// Ranks management page
router.get('/staff/ranks', ensureAuthenticated, async (req, res) => {
    try {
        if (!req.user.isGlobalAdmin) {
            req.flash('error', 'Access denied.');
            return res.redirect('/home');
        }

        const guilds = req.user.guilds || [];
        const selectedGuildId = req.query.guild || (guilds.length > 0 ? guilds[0].id : null);

        if (!selectedGuildId) {
            var theme = jsonfile.readFileSync(themes);
            return res.render('staff/ranks', {
                title: 'Rank Management',
                guilds: [],
                selectedGuild: null,
                ranks: [],
                theme: theme,
                profile: req.user
            });
        }

        const ranks = await StaffRank.getGuildRanks(selectedGuildId);
        const selectedGuild = guilds.find(g => g.id === selectedGuildId);
        const guildSettings = await GuildSettings.getGuildSettings(selectedGuildId, selectedGuild?.name);

        // Get Discord guild roles
        let guildRoles = [];
        try {
            const client = req.app.get('discordClient');
            if (client) {
                const guild = await client.guilds.fetch(selectedGuildId);
                guildRoles = guild.roles.cache
                    .filter(role => role.id !== guild.id) // Exclude @everyone
                    .sort((a, b) => b.position - a.position)
                    .map(role => ({
                        id: role.id,
                        name: role.name,
                        color: role.hexColor
                    }));
            }
        } catch (error) {
            console.error('Error fetching guild roles:', error);
        }

        var theme = jsonfile.readFileSync(themes);
        res.render('staff/ranks', {
            title: 'Rank Management',
            guilds,
            selectedGuild,
            ranks,
            guildSettings,
            guildRoles,
            theme: theme,
            profile: req.user
        });
    } catch (error) {
        console.error('Error loading ranks page:', error);
        req.flash('error', 'An error occurred while loading the ranks page.');
        res.redirect('/staff');
    }
});

// Create new rank
router.post('/staff/ranks/create', ensureAuthenticated, async (req, res) => {
    try {
        if (!req.user.isGlobalAdmin) {
            return res.status(403).json({ error: 'Access denied' });
        }

        const { guildId, name, roleId, permissions, color, description } = req.body;

        // Validate required fields
        if (!guildId || !name || !roleId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Check if role already exists
        const existingRank = await StaffRank.findOne({ roleId });
        if (existingRank) {
            return res.status(400).json({ error: 'A rank with this role already exists' });
        }

        // Get next level
        const nextLevel = await StaffRank.getNextLevel(guildId);

        // Create new rank
        const newRank = new StaffRank({
            name: name.trim(),
            guildId,
            roleId,
            level: nextLevel,
            permissions: {
                canHire: permissions?.canHire === 'on',
                canFire: permissions?.canFire === 'on',
                canPromote: permissions?.canPromote === 'on',
                canDemote: permissions?.canDemote === 'on',
                canSuspend: permissions?.canSuspend === 'on',
                canViewLogs: permissions?.canViewLogs === 'on',
                canManageRanks: permissions?.canManageRanks === 'on'
            },
            color: color || '#99AAB5',
            description: description || '',
            createdBy: req.user.id
        });

        await newRank.save();

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'RANK_CREATED',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: newRank._id.toString(),
                name: newRank.name,
                type: 'RANK'
            },
            details: {
                level: newRank.level,
                permissions: newRank.permissions
            },
            metadata: {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent')
            }
        });

        res.json({ success: true, rank: newRank });
    } catch (error) {
        console.error('Error creating rank:', error);
        res.status(500).json({ error: 'Failed to create rank' });
    }
});

// Update rank
router.post('/staff/ranks/update/:id', ensureAuthenticated, async (req, res) => {
    try {
        if (!req.user.isGlobalAdmin) {
            return res.status(403).json({ error: 'Access denied' });
        }

        const rankId = req.params.id;
        const { name, permissions, color, description } = req.body;

        const rank = await StaffRank.findById(rankId);
        if (!rank) {
            return res.status(404).json({ error: 'Rank not found' });
        }

        const oldData = { ...rank.toObject() };

        // Update rank
        rank.name = name?.trim() || rank.name;
        rank.permissions = {
            canHire: permissions?.canHire === 'on',
            canFire: permissions?.canFire === 'on',
            canPromote: permissions?.canPromote === 'on',
            canDemote: permissions?.canDemote === 'on',
            canSuspend: permissions?.canSuspend === 'on',
            canViewLogs: permissions?.canViewLogs === 'on',
            canManageRanks: permissions?.canManageRanks === 'on'
        };
        rank.color = color || rank.color;
        rank.description = description || rank.description;

        await rank.save();

        // Log the action
        await StaffLog.logAction({
            guildId: rank.guildId,
            action: 'RANK_UPDATED',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: rank._id.toString(),
                name: rank.name,
                type: 'RANK'
            },
            details: {
                oldData,
                newData: rank.toObject()
            },
            metadata: {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent')
            }
        });

        res.json({ success: true, rank });
    } catch (error) {
        console.error('Error updating rank:', error);
        res.status(500).json({ error: 'Failed to update rank' });
    }
});

// Update rank levels (reorder)
router.post('/staff/ranks/reorder', ensureAuthenticated, async (req, res) => {
    try {
        if (!req.user.isGlobalAdmin) {
            return res.status(403).json({ error: 'Access denied' });
        }

        const { guildId, rankOrder } = req.body;

        if (!Array.isArray(rankOrder)) {
            return res.status(400).json({ error: 'Invalid rank order data' });
        }

        // Update levels based on order (highest level = highest in hierarchy)
        const updatePromises = rankOrder.map((rankId, index) => {
            const level = rankOrder.length - index; // Reverse order for levels
            return StaffRank.findByIdAndUpdate(rankId, { level });
        });

        await Promise.all(updatePromises);

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'RANK_UPDATED',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                type: 'RANK'
            },
            details: {
                action: 'reorder',
                newOrder: rankOrder
            },
            metadata: {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent')
            }
        });

        res.json({ success: true });
    } catch (error) {
        console.error('Error reordering ranks:', error);
        res.status(500).json({ error: 'Failed to reorder ranks' });
    }
});

// Delete rank
router.post('/staff/ranks/delete/:id', ensureAuthenticated, async (req, res) => {
    try {
        if (!req.user.isGlobalAdmin) {
            return res.status(403).json({ error: 'Access denied' });
        }

        const rankId = req.params.id;
        const rank = await StaffRank.findById(rankId);
        
        if (!rank) {
            return res.status(404).json({ error: 'Rank not found' });
        }

        // Check if any staff members have this rank
        const staffCount = await StaffMember.countDocuments({
            rankId: rankId,
            isActive: true
        });

        if (staffCount > 0) {
            return res.status(400).json({ 
                error: `Cannot delete rank. ${staffCount} staff member(s) currently have this rank.` 
            });
        }

        // Soft delete the rank
        rank.isActive = false;
        await rank.save();

        // Log the action
        await StaffLog.logAction({
            guildId: rank.guildId,
            action: 'RANK_DELETED',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: rank._id.toString(),
                name: rank.name,
                type: 'RANK'
            },
            details: {
                level: rank.level
            },
            metadata: {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent')
            }
        });

        res.json({ success: true });
    } catch (error) {
        console.error('Error deleting rank:', error);
        res.status(500).json({ error: 'Failed to delete rank' });
    }
});

// Update dashboard settings
router.post('/staff/settings/dashboard', ensureAuthenticated, async (req, res) => {
    try {
        if (!req.user.isGlobalAdmin) {
            return res.status(403).json({ error: 'Access denied' });
        }

        const { guildId, dashboardRoleId, defaultStaffRoleId, suspendedRoleId } = req.body;

        if (!guildId) {
            return res.status(400).json({ error: 'Guild ID is required' });
        }

        // Get guild settings
        const guildSettings = await GuildSettings.getGuildSettings(guildId);

        // Update settings
        guildSettings.dashboardRoleId = dashboardRoleId || null;
        guildSettings.defaultStaffRoleId = defaultStaffRoleId || null;
        guildSettings.suspendedRoleId = suspendedRoleId || null;
        await guildSettings.save();

        res.json({
            success: true,
            message: 'Dashboard settings updated successfully'
        });
    } catch (error) {
        console.error('Error updating dashboard settings:', error);
        res.status(500).json({ error: 'Failed to update dashboard settings' });
    }
});

// Staff management actions
router.post('/staff/manage/promote', ensureAuthenticated, async (req, res) => {
    try {
        const { memberId, guildId, newRankId } = req.body;

        if (!memberId || !guildId || !newRankId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Get staff member
        const staffMember = await StaffMember.findById(memberId).populate('rankId');
        if (!staffMember) {
            return res.status(404).json({ error: 'Staff member not found' });
        }

        // Get new rank
        const newRank = await StaffRank.findById(newRankId);
        if (!newRank) {
            return res.status(404).json({ error: 'Rank not found' });
        }

        // Check if promotion is valid (higher level)
        if (newRank.level <= staffMember.rankId.level) {
            return res.status(400).json({ error: 'New rank must be higher than current rank' });
        }

        // Update Discord roles
        const client = req.app.get('discordClient');
        if (client) {
            try {
                const guild = await client.guilds.fetch(guildId);
                const member = await guild.members.fetch(staffMember.userId);

                // Remove old role and add new role
                if (staffMember.rankId.roleId) {
                    await member.roles.remove(staffMember.rankId.roleId);
                }
                if (newRank.roleId) {
                    await member.roles.add(newRank.roleId);
                }
            } catch (discordError) {
                console.error('Discord role update error:', discordError);
                return res.status(500).json({ error: 'Failed to update Discord roles' });
            }
        }

        // Update database
        const oldRank = staffMember.rankId;
        staffMember.rankId = newRankId;
        await staffMember.save();

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'PROMOTE',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: staffMember.userId,
                username: staffMember.username,
                discriminator: staffMember.discriminator,
                type: 'USER'
            },
            details: {
                oldRank: oldRank.name,
                newRank: newRank.name,
                reason: 'Promoted via dashboard'
            }
        });

        res.json({ success: true, message: 'Member promoted successfully' });
    } catch (error) {
        console.error('Error promoting member:', error);
        res.status(500).json({ error: 'Failed to promote member' });
    }
});

router.post('/staff/manage/demote', ensureAuthenticated, async (req, res) => {
    try {
        const { memberId, guildId, newRankId } = req.body;

        if (!memberId || !guildId || !newRankId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Get staff member
        const staffMember = await StaffMember.findById(memberId).populate('rankId');
        if (!staffMember) {
            return res.status(404).json({ error: 'Staff member not found' });
        }

        // Get new rank
        const newRank = await StaffRank.findById(newRankId);
        if (!newRank) {
            return res.status(404).json({ error: 'Rank not found' });
        }

        // Check if demotion is valid (lower level)
        if (newRank.level >= staffMember.rankId.level) {
            return res.status(400).json({ error: 'New rank must be lower than current rank' });
        }

        // Update Discord roles
        const client = req.app.get('discordClient');
        if (client) {
            try {
                const guild = await client.guilds.fetch(guildId);
                const member = await guild.members.fetch(staffMember.userId);

                // Remove old role and add new role
                if (staffMember.rankId.roleId) {
                    await member.roles.remove(staffMember.rankId.roleId);
                }
                if (newRank.roleId) {
                    await member.roles.add(newRank.roleId);
                }
            } catch (discordError) {
                console.error('Discord role update error:', discordError);
                return res.status(500).json({ error: 'Failed to update Discord roles' });
            }
        }

        // Update database
        const oldRank = staffMember.rankId;
        staffMember.rankId = newRankId;
        await staffMember.save();

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'DEMOTE',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: staffMember.userId,
                username: staffMember.username,
                discriminator: staffMember.discriminator,
                type: 'USER'
            },
            details: {
                oldRank: oldRank.name,
                newRank: newRank.name,
                reason: 'Demoted via dashboard'
            }
        });

        res.json({ success: true, message: 'Member demoted successfully' });
    } catch (error) {
        console.error('Error demoting member:', error);
        res.status(500).json({ error: 'Failed to demote member' });
    }
});

router.post('/staff/manage/suspend', ensureAuthenticated, async (req, res) => {
    try {
        console.log('[Dashboard] Suspend request received:', req.body);
        console.log('[Dashboard] User:', req.user);

        const { memberId, guildId, duration, durationUnit, reason } = req.body;

        if (!memberId || !guildId || !duration) {
            console.log('[Dashboard] Missing required fields:', { memberId, guildId, duration });
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Check permissions
        const guildSettings = await GuildSettings.getGuildSettings(guildId);
        const discordClient = req.app.get('discordClient');
        let member = null;

        if (discordClient) {
            try {
                const guild = await discordClient.guilds.fetch(guildId);
                member = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('[Dashboard] Could not fetch member data:', err.message);
            }
        }

        if (!guildSettings.canAccessDashboard(req.user.id, member)) {
            console.log('[Dashboard] Access denied for user:', req.user.id);
            return res.status(403).json({ error: 'Access denied' });
        }

        // Get staff member
        const staffMember = await StaffMember.findById(memberId).populate('rankId');
        if (!staffMember) {
            return res.status(404).json({ error: 'Staff member not found' });
        }

        if (staffMember.isSuspended) {
            return res.status(400).json({ error: 'Member is already suspended' });
        }

        // Calculate suspension end time based on unit
        let durationMs;
        if (durationUnit === 'minutes') {
            durationMs = duration * 60 * 1000; // minutes to milliseconds
        } else {
            durationMs = duration * 60 * 60 * 1000; // hours to milliseconds (default)
        }
        const suspensionEnd = new Date(Date.now() + durationMs);

        // Update Discord roles
        if (discordClient) {
            try {
                const guild = await discordClient.guilds.fetch(guildId);
                const discordMember = await guild.members.fetch(staffMember.userId);

                // Store current roles for restoration later
                const currentRoles = discordMember.roles.cache
                    .filter(role => role.id !== guild.id)
                    .map(role => role.id);

                // Remove staff role and default staff role
                if (staffMember.rankId.roleId) {
                    await discordMember.roles.remove(staffMember.rankId.roleId);
                }
                if (guildSettings.defaultStaffRoleId) {
                    await discordMember.roles.remove(guildSettings.defaultStaffRoleId);
                }

                // Add suspended role
                if (guildSettings.suspendedRoleId) {
                    await discordMember.roles.add(guildSettings.suspendedRoleId);
                }

                // Store roles for restoration
                staffMember.previousRoles = currentRoles;
            } catch (discordError) {
                console.error('Discord role update error:', discordError);
                return res.status(500).json({ error: 'Failed to update Discord roles' });
            }
        }

        // Update database
        staffMember.isSuspended = true;
        staffMember.suspensionStart = new Date();
        staffMember.suspensionEnd = suspensionEnd;
        staffMember.suspensionReason = reason || 'No reason provided';
        staffMember.suspendedBy = req.user.id;
        await staffMember.save();

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'SUSPEND',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: staffMember.userId,
                username: staffMember.username,
                discriminator: staffMember.discriminator,
                type: 'USER'
            },
            details: {
                duration: `${duration} hours`,
                reason: reason || 'No reason provided',
                suspensionEnd: suspensionEnd.toISOString()
            }
        });

        res.json({ success: true, message: 'Member suspended successfully' });
    } catch (error) {
        console.error('Error suspending member:', error);
        res.status(500).json({ error: 'Failed to suspend member' });
    }
});

router.post('/staff/manage/unsuspend', ensureAuthenticated, async (req, res) => {
    try {
        const { memberId, guildId } = req.body;

        if (!memberId || !guildId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Get staff member
        const staffMember = await StaffMember.findById(memberId).populate('rankId');
        if (!staffMember) {
            return res.status(404).json({ error: 'Staff member not found' });
        }

        if (!staffMember.isSuspended) {
            return res.status(400).json({ error: 'Member is not suspended' });
        }

        // Get guild settings for suspended role
        const guildSettings = await GuildSettings.getGuildSettings(guildId);

        // Update Discord roles
        const client = req.app.get('discordClient');
        if (client) {
            try {
                const guild = await client.guilds.fetch(guildId);
                const member = await guild.members.fetch(staffMember.userId);

                // Remove suspended role
                if (guildSettings.suspendedRoleId) {
                    await member.roles.remove(guildSettings.suspendedRoleId);
                }

                // Restore previous roles if available
                if (staffMember.previousRoles && staffMember.previousRoles.length > 0) {
                    try {
                        await member.roles.add(staffMember.previousRoles);
                    } catch (roleError) {
                        console.warn('Failed to restore some roles:', roleError);
                        // Fallback to adding basic roles
                        if (staffMember.rankId.roleId) {
                            await member.roles.add(staffMember.rankId.roleId);
                        }
                        if (guildSettings.defaultStaffRoleId) {
                            await member.roles.add(guildSettings.defaultStaffRoleId);
                        }
                    }
                } else {
                    // Fallback: add rank role and default staff role
                    if (staffMember.rankId.roleId) {
                        await member.roles.add(staffMember.rankId.roleId);
                    }
                    if (guildSettings.defaultStaffRoleId) {
                        await member.roles.add(guildSettings.defaultStaffRoleId);
                    }
                }
            } catch (discordError) {
                console.error('Discord role update error:', discordError);
                return res.status(500).json({ error: 'Failed to update Discord roles' });
            }
        }

        // Update database
        staffMember.isSuspended = false;
        staffMember.suspensionStart = null;
        staffMember.suspensionEnd = null;
        staffMember.suspensionReason = null;
        staffMember.suspendedBy = null;
        await staffMember.save();

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'UNSUSPEND',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: staffMember.userId,
                username: staffMember.username,
                discriminator: staffMember.discriminator,
                type: 'USER'
            },
            details: {
                reason: 'Unsuspended via dashboard'
            }
        });

        res.json({ success: true, message: 'Member unsuspended successfully' });
    } catch (error) {
        console.error('Error unsuspending member:', error);
        res.status(500).json({ error: 'Failed to unsuspend member' });
    }
});

router.post('/staff/manage/fire', ensureAuthenticated, async (req, res) => {
    try {
        console.log('[Dashboard] Fire request received:', req.body);
        console.log('[Dashboard] User:', req.user);

        const { memberId, guildId } = req.body;

        if (!memberId || !guildId) {
            console.log('[Dashboard] Missing required fields:', { memberId, guildId });
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Check permissions
        const fireGuildSettings = await GuildSettings.getGuildSettings(guildId);
        const fireDiscordClient = req.app.get('discordClient');
        let fireMember = null;

        if (fireDiscordClient) {
            try {
                const guild = await fireDiscordClient.guilds.fetch(guildId);
                fireMember = await guild.members.fetch(req.user.id);
            } catch (err) {
                console.log('[Dashboard] Could not fetch member data:', err.message);
            }
        }

        if (!fireGuildSettings.canAccessDashboard(req.user.id, fireMember)) {
            console.log('[Dashboard] Access denied for user:', req.user.id);
            return res.status(403).json({ error: 'Access denied' });
        }

        // Get staff member
        const staffMember = await StaffMember.findById(memberId).populate('rankId');
        if (!staffMember) {
            return res.status(404).json({ error: 'Staff member not found' });
        }

        if (!staffMember.isActive) {
            return res.status(400).json({ error: 'Member is already fired' });
        }

        // Get guild settings for suspended role (in case they're suspended) - reuse from permission check

        // Update Discord roles
        if (fireDiscordClient) {
            try {
                const guild = await fireDiscordClient.guilds.fetch(guildId);
                const fireDiscordMember = await guild.members.fetch(staffMember.userId);

                // Remove all staff-related roles
                if (staffMember.rankId.roleId) {
                    await fireDiscordMember.roles.remove(staffMember.rankId.roleId);
                }
                if (staffMember.isSuspended && fireGuildSettings.suspendedRoleId) {
                    await fireDiscordMember.roles.remove(fireGuildSettings.suspendedRoleId);
                }
            } catch (discordError) {
                console.error('Discord role update error:', discordError);
                return res.status(500).json({ error: 'Failed to update Discord roles' });
            }
        }

        // Update database
        staffMember.isActive = false;
        staffMember.firedAt = new Date();
        staffMember.firedBy = req.user.id;
        // Clear suspension if they were suspended
        staffMember.isSuspended = false;
        staffMember.suspensionStart = null;
        staffMember.suspensionEnd = null;
        staffMember.suspensionReason = null;
        staffMember.suspendedBy = null;
        await staffMember.save();

        // Log the action
        await StaffLog.logAction({
            guildId,
            action: 'FIRE',
            executor: {
                id: req.user.id,
                username: req.user.username,
                discriminator: req.user.discriminator
            },
            target: {
                id: staffMember.userId,
                username: staffMember.username,
                discriminator: staffMember.discriminator,
                type: 'USER'
            },
            details: {
                rank: staffMember.rankId.name,
                reason: 'Fired via dashboard'
            }
        });

        res.json({ success: true, message: 'Member fired successfully' });
    } catch (error) {
        console.error('Error firing member:', error);
        res.status(500).json({ error: 'Failed to fire member' });
    }
});

module.exports = router;
