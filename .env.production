# Discord Bot Configuration
DISCORD_TOKEN=MTM5NDQ4NjQ5MzI1MDE5MTU1Mw.GgikUD.HyF0qd1IkbSLe29mdMz2JigTaMaZjhUOWu4ajA
DISCORD_CLIENT_ID=1394486493250191553
DISCORD_CLIENT_SECRET=zuqdtUuVhAEFUYhFL1AEXL2jrn3DYS41

# MongoDB Configuration
MONGODB_URI=************************************************************************************

# Dashboard Configuration
DASHBOARD_PORT=3001
DASHBOARD_SECRET=faefewgejtgeuyghgoiehguihoi
DASHBOARD_CALLBACK_URL=https://your-domain.com/login/api

# Logging Configuration
LOG_LEVEL=info
LOG_CHANNEL_ID=1395991220957741126
ERROR_LOG_CHANNEL_ID=1395991220957741126
STAFF_LOG_CHANNEL_ID=1395991220957741126

# Bot Configuration
BOT_PREFIX=!
GUILD_ID=1315075703473045615

# Admin Configuration (comma-separated user IDs)
ADMIN_USER_IDS=299729731237052417

# Environment
NODE_ENV=production

# Application Webhook Configuration
APPLICATION_WEBHOOK_URL=https://discord.com/api/webhooks/1395991220957741126/YOUR_WEBHOOK_TOKEN_HERE
