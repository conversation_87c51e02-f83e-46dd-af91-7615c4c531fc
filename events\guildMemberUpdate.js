const { Events } = require('discord.js');
const { StaffMember, StaffRank, StaffLog } = require('../models');
const StaffUtils = require('../utils/staffUtils');

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember) {
        try {
            // Check if roles changed
            const oldRoles = oldMember.roles.cache;
            const newRoles = newMember.roles.cache;
            
            // Find added roles
            const addedRoles = newRoles.filter(role => !oldRoles.has(role.id));
            // Find removed roles
            const removedRoles = oldRoles.filter(role => !newRoles.has(role.id));

            // Get all staff ranks for this guild
            const staffRanks = await StaffRank.find({ 
                guildId: newMember.guild.id, 
                isActive: true 
            });

            // Check if any added roles are staff roles
            for (const [roleId, role] of addedRoles) {
                const staffRank = staffRanks.find(rank => rank.roleId === roleId);
                if (staffRank) {
                    console.log(`[RoleUpdate] User ${newMember.user.username} gained staff role: ${staffRank.name}`);
                    
                    // Check if user is already in staff database
                    const existingStaff = await StaffMember.findOne({
                        userId: newMember.id,
                        guildId: newMember.guild.id,
                        isActive: true
                    });

                    if (!existingStaff) {
                        // Auto-hire with role assignment detection
                        try {
                            const newStaffMember = new StaffMember({
                                userId: newMember.id,
                                username: newMember.user.username,
                                discriminator: newMember.user.discriminator,
                                guildId: newMember.guild.id,
                                rankId: staffRank._id,
                                hiredAt: new Date(),
                                hiredBy: 'Role Assignment',
                                hiredByUserId: 'ROLE_GRANT',
                                isActive: true,
                                isSuspended: false
                            });

                            await newStaffMember.save();
                        } catch (saveError) {
                            if (saveError.code === 11000) {
                                console.log(`[RoleUpdate] User ${newMember.user.username} already exists in database, skipping creation`);
                                return; // Skip if already exists
                            }
                            throw saveError; // Re-throw other errors
                        }

                        console.log(`[RoleUpdate] Auto-hired ${newMember.user.username} as ${staffRank.name} due to role assignment`);

                        // Log the action
                        await StaffLog.logAction({
                            guildId: newMember.guild.id,
                            action: 'HIRE',
                            executor: {
                                id: 'ROLE_SYSTEM',
                                username: 'Role System',
                                discriminator: '0000'
                            },
                            target: {
                                id: newMember.id,
                                username: newMember.user.username,
                                discriminator: newMember.user.discriminator,
                                type: 'USER'
                            },
                            details: {
                                rank: staffRank.name,
                                reason: 'Auto-hired due to Discord role assignment'
                            }
                        });
                    } else if (existingStaff.rankId.toString() !== staffRank._id.toString()) {
                        // Update rank if different
                        const oldRank = await StaffRank.findById(existingStaff.rankId);
                        existingStaff.rankId = staffRank._id;
                        await existingStaff.save();

                        console.log(`[RoleUpdate] Updated ${newMember.user.username} rank from ${oldRank?.name} to ${staffRank.name}`);

                        // Log the promotion/demotion
                        await StaffLog.logAction({
                            guildId: newMember.guild.id,
                            action: staffRank.level > (oldRank?.level || 0) ? 'PROMOTE' : 'DEMOTE',
                            executor: {
                                id: 'ROLE_SYSTEM',
                                username: 'Role System',
                                discriminator: '0000'
                            },
                            target: {
                                id: newMember.id,
                                username: newMember.user.username,
                                discriminator: newMember.user.discriminator,
                                type: 'USER'
                            },
                            details: {
                                oldRank: oldRank?.name || 'Unknown',
                                newRank: staffRank.name,
                                reason: 'Auto-updated due to Discord role change'
                            }
                        });
                    }
                }
            }

            // Check if any removed roles are staff roles
            for (const [roleId, role] of removedRoles) {
                const staffRank = staffRanks.find(rank => rank.roleId === roleId);
                if (staffRank) {
                    console.log(`[RoleUpdate] User ${newMember.user.username} lost staff role: ${staffRank.name}`);
                    
                    // Check if user should be fired (no longer has any staff roles)
                    const hasOtherStaffRoles = staffRanks.some(rank => 
                        rank.roleId !== roleId && newMember.roles.cache.has(rank.roleId)
                    );

                    if (!hasOtherStaffRoles) {
                        // Check if user is suspended before auto-firing
                        const staffMember = await StaffMember.findOne({
                            userId: newMember.id,
                            guildId: newMember.guild.id,
                            isActive: true
                        });

                        if (staffMember) {
                            // Don't auto-fire if they're suspended
                            if (staffMember.isSuspended) {
                                console.log(`[RoleUpdate] User ${newMember.user.username} lost staff role but is suspended, not auto-firing`);
                                return; // Skip auto-firing for suspended members
                            }

                            // Fire the user only if not suspended
                            staffMember.isActive = false;
                            staffMember.firedAt = new Date();
                            staffMember.firedBy = 'Role Removal';
                            await staffMember.save();

                            console.log(`[RoleUpdate] Auto-fired ${newMember.user.username} due to role removal`);

                            // Log the action
                            await StaffLog.logAction({
                                guildId: newMember.guild.id,
                                action: 'FIRE',
                                executor: {
                                    id: 'ROLE_SYSTEM',
                                    username: 'Role System',
                                    discriminator: '0000'
                                },
                                target: {
                                    id: newMember.id,
                                    username: newMember.user.username,
                                    discriminator: newMember.user.discriminator,
                                    type: 'USER'
                                },
                                details: {
                                    rank: staffRank.name,
                                    reason: 'Auto-fired due to Discord role removal'
                                }
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error('[RoleUpdate] Error handling role update:', error);
        }
    }
};
