<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title><%= title %> - Nexoria Staff Dashboard</title>
    <link href="https://fonts.googleapis.com/css?family=Fira+Sans:400,500,600" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/open-iconic/1.1.1/font/css/open-iconic-bootstrap.min.css">
    <link href="https://cdn.jsdelivr.net/gh/hung1001/font-awesome-pro@0ac23ca/css/all.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.9/flatpickr.min.js">
    <link rel="stylesheet" href="../<%= typeof theme !== 'undefined' ? theme.theme : 'home/assets/stylesheets/theme.css' %>" data-skin="default">
    <style>
        .staff-card {
            transition: transform 0.2s;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .staff-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .rank-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .status-active {
            color: #57f287;
        }
        .status-suspended {
            color: #ed4245;
        }
        .log-entry {
            border-left: 3px solid #5865f2;
            padding-left: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="app-header app-header-dark">
            <div class="top-bar">
                <div class="top-bar-brand">
                    <h5>🛡️ Nexoria Staff Dashboard</h5>
                </div>
                <div class="top-bar-list">
                    <div class="top-bar-item px-2 d-md-none d-lg-none d-xl-none">
                        <button class="hamburger hamburger-squeeze" type="button" data-toggle="aside" aria-label="toggle menu">
                            <span class="hamburger-box"><span class="hamburger-inner"></span></span>
                        </button>
                    </div>
                    <div class="top-bar-item top-bar-item-right px-0 d-none d-sm-flex">
                        <div class="dropdown d-none d-md-flex">
                            <button class="btn-account d-none d-md-flex" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="user-avatar user-avatar-md">
                                    <img src="https://cdn.discordapp.com/avatars/<%= typeof profile !== 'undefined' ? profile.id : '' %>/<%= typeof profile !== 'undefined' ? profile.avatar : '' %>" alt="">
                                </span>
                                <span class="account-summary pr-lg-4 d-none d-lg-block">
                                    <span class="account-name"><%= typeof profile !== 'undefined' ? profile.username : 'User' %></span>
                                    <span class="account-description">Staff Manager</span>
                                </span>
                            </button>
                            <div class="dropdown-menu">
                                <div class="dropdown-arrow d-lg-none" x-arrow=""></div>
                                <div class="dropdown-arrow ml-3 d-none d-lg-block"></div>
                                <h6 class="dropdown-header d-none d-md-block d-lg-none"><%= typeof profile !== 'undefined' ? profile.username : 'User' %></h6>
                                <a class="dropdown-item" href="/logout">
                                    <span class="dropdown-icon oi oi-account-logout"></span> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <aside class="app-aside app-aside-expand-md app-aside-light">
            <div class="aside-content">
                <header class="aside-header d-block d-md-none">
                    <button class="btn-account" type="button" data-toggle="collapse" data-target="#dropdown-aside">
                        <span class="user-avatar user-avatar-lg">
                            <img src="https://cdn.discordapp.com/avatars/<%= typeof profile !== 'undefined' ? profile.id : '' %>/<%= typeof profile !== 'undefined' ? profile.avatar : '' %>" alt="">
                        </span>
                        <span class="account-icon"><span class="fa fa-caret-down fa-lg"></span></span>
                        <span class="account-summary">
                            <span class="account-name"><%= typeof profile !== 'undefined' ? profile.username : 'User' %></span>
                            <span class="account-description">Staff Manager</span>
                        </span>
                    </button>
                    <div id="dropdown-aside" class="dropdown-aside collapse">
                        <div class="pb-3">
                            <a class="dropdown-item" href="/logout">
                                <span class="dropdown-icon oi oi-account-logout"></span> Logout
                            </a>
                        </div>
                    </div>
                </header>
                <div class="aside-menu overflow-hidden">
                    <nav id="stacked-menu" class="stacked-menu">
                        <ul class="menu">
                            <li class="menu-item">
                                <a href="/home" class="menu-link">
                                    <span class="menu-icon fas fa-home"></span>
                                    <span class="menu-text">Dashboard</span>
                                </a>
                            </li>
                            <li class="menu-item has-active">
                                <a href="/staff" class="menu-link">
                                    <span class="menu-icon fas fa-users"></span>
                                    <span class="menu-text">Staff Management</span>
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="/guilds" class="menu-link">
                                    <span class="menu-icon fas fa-server"></span>
                                    <span class="menu-text">Servers</span>
                                </a>
                            </li>
                            <% if (typeof profile !== 'undefined' && profile.isGlobalAdmin) { %>
                            <li class="menu-item">
                                <a href="/admin" class="menu-link">
                                    <span class="menu-icon fas fa-shield-alt"></span>
                                    <span class="menu-text">Admin Panel</span>
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="/settings" class="menu-link">
                                    <span class="menu-icon fas fa-cog"></span>
                                    <span class="menu-text">Settings</span>
                                </a>
                            </li>
                            <% } %>
                        </ul>
                    </nav>
                </div>
            </div>
        </aside>

        <main class="app-main">
            <div class="wrapper">
                <div class="page">
                    <div class="page-inner">
                        <header class="page-title-bar">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="/home"><i class="breadcrumb-icon fas fa-home mr-2"></i>Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        <a href="/staff"><i class="breadcrumb-icon fas fa-users mr-2"></i>Staff Management</a>
                                    </li>
                                </ol>
                            </nav>
                            <h1 class="page-title">Staff Management</h1>
                            <% if (selectedGuild) { %>
                            <p class="text-muted">Managing staff for <%= selectedGuild.name %></p>
                            <% } %>
                        </header>

                        <div class="page-section">
                            <div class="section-block">
                                <!-- Guild Selector -->
                                <% if (guilds.length > 0) { %>
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="guildSelector" class="form-label">Select Server:</label>
                                        <select class="form-control" id="guildSelector" onchange="changeGuild()">
                                            <% guilds.forEach(guild => { %>
                                            <option value="<%= guild.id %>" <%= selectedGuild && selectedGuild.id === guild.id ? 'selected' : '' %>>
                                                <%= guild.name %>
                                            </option>
                                            <% }); %>
                                        </select>
                                    </div>
                                </div>
                                <% } %>

                                <% if (!selectedGuild) { %>
                                <div class="card">
                                    <div class="card-body text-center py-5">
                                        <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                                        <h4>No Server Selected</h4>
                                        <p class="text-muted">Please select a server to manage staff members.</p>
                                        <a href="/staff" class="btn btn-primary">
                                            <i class="fas fa-arrow-left"></i> Back to Server Selection
                                        </a>
                                    </div>
                                </div>
                                <% } else { %>

                                <!-- Navigation Tabs -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <ul class="nav nav-tabs card-header-tabs">
                                            <li class="nav-item">
                                                <a class="nav-link active" href="/staff?guild=<%= selectedGuild.id %>">
                                                    <i class="fas fa-users"></i> Staff Overview
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" href="/applications?guild=<%= selectedGuild.id %>">
                                                    <i class="fas fa-file-alt"></i> Applications
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" href="/staff/ranks?guild=<%= selectedGuild.id %>">
                                                    <i class="fas fa-layer-group"></i> Rank Management
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Stats Cards -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4><%= staffMembers.length %></h4>
                                                        <p class="mb-0">Total Staff</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-users fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4><%= staffMembers.filter(s => !s.isSuspended).length %></h4>
                                                        <p class="mb-0">Active Staff</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-check-circle fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4><%= staffMembers.filter(s => s.isSuspended).length %></h4>
                                                        <p class="mb-0">Suspended</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-pause-circle fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4><%= ranks.length %></h4>
                                                        <p class="mb-0">Ranks</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-layer-group fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Staff Members -->
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5><i class="fas fa-users"></i> Staff Members</h5>
                                                <button class="btn btn-sm btn-outline-primary" onclick="refreshStaffData()" title="Refresh Staff Data">
                                                    <i class="fas fa-sync-alt"></i> Refresh
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <% const activeMembers = staffMembers.filter(member => !member.isSuspended); %>
                                                <% if (activeMembers.length === 0) { %>
                                                <p class="text-muted">No active staff members found.</p>
                                                <% } else { %>
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>Member</th>
                                                                <th>Rank</th>
                                                                <th>Status</th>
                                                                <th>Hired Date</th>
                                                                <th>Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <% activeMembers.forEach(member => { %>
                                                            <tr>
                                                                <td>
                                                                    <strong><%= member.username %>#<%= member.discriminator %></strong><br>
                                                                    <small class="text-muted">ID: <%= member.userId %></small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge rank-badge" style="background-color: <%= member.rankId.color %>">
                                                                        <%= member.rankId.name %> (Level <%= member.rankId.level %>)
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <% if (member.isSuspended) { %>
                                                                    <i class="fas fa-pause-circle status-suspended"></i> Suspended
                                                                    <% if (member.suspensionEnd) { %>
                                                                    <br><small class="text-muted">Until: <%= new Date(member.suspensionEnd).toLocaleDateString() %></small>
                                                                    <% } %>
                                                                    <% } else { %>
                                                                    <i class="fas fa-check-circle status-active"></i> Active
                                                                    <% } %>
                                                                </td>
                                                                <td>
                                                                    <%= new Date(member.hiredAt).toLocaleDateString() %>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group" role="group">
                                                                        <!-- Promote Button -->
                                                                        <button class="btn btn-sm btn-success"
                                                                                onclick="showPromoteModal('<%= member._id %>', '<%= member.username %>', '<%= member.rankId._id %>')"
                                                                                title="Promote">
                                                                            <i class="fas fa-arrow-up"></i>
                                                                        </button>

                                                                        <!-- Demote Button -->
                                                                        <button class="btn btn-sm btn-warning"
                                                                                onclick="showDemoteModal('<%= member._id %>', '<%= member.username %>', '<%= member.rankId._id %>')"
                                                                                title="Demote">
                                                                            <i class="fas fa-arrow-down"></i>
                                                                        </button>

                                                                        <!-- Suspend/Unsuspend Button -->
                                                                        <% if (member.isSuspended) { %>
                                                                        <button class="btn btn-sm btn-info"
                                                                                onclick="unsuspendMember('<%= member._id %>', '<%= member.username %>')"
                                                                                title="Unsuspend">
                                                                            <i class="fas fa-play"></i>
                                                                        </button>
                                                                        <% } else { %>
                                                                        <button class="btn btn-sm btn-secondary"
                                                                                onclick="showSuspendModal('<%= member._id %>', '<%= member.username %>')"
                                                                                title="Suspend">
                                                                            <i class="fas fa-pause"></i>
                                                                        </button>
                                                                        <% } %>

                                                                        <!-- Fire Button -->
                                                                        <button class="btn btn-sm btn-danger"
                                                                                onclick="fireMember('<%= member._id %>', '<%= member.username %>')"
                                                                                title="Fire">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <% }); %>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <% } %>
                                            </div>
                                        </div>

                                        <!-- Suspended Members -->
                                        <% const suspendedMembers = staffMembers.filter(member => member.isSuspended); %>
                                        <% if (suspendedMembers.length > 0) { %>
                                        <div class="card mt-4">
                                            <div class="card-header">
                                                <h5><i class="fas fa-pause-circle text-warning"></i> Suspended Members</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>Member</th>
                                                                <th>Rank</th>
                                                                <th>Suspended Until</th>
                                                                <th>Reason</th>
                                                                <th>Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <% suspendedMembers.forEach(member => { %>
                                                            <tr>
                                                                <td>
                                                                    <strong><%= member.username %>#<%= member.discriminator %></strong>
                                                                    <br><small class="text-muted">ID: <%= member.userId %></small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge rank-badge" style="background-color: <%= member.rankId.color %>">
                                                                        <%= member.rankId.name %> (Level <%= member.rankId.level %>)
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <% if (member.suspensionEnd) { %>
                                                                    <%= new Date(member.suspensionEnd).toLocaleString() %>
                                                                    <br><small class="text-muted">
                                                                        <% const timeLeft = new Date(member.suspensionEnd) - new Date(); %>
                                                                        <% if (timeLeft > 0) { %>
                                                                            <% const hours = Math.floor(timeLeft / (1000 * 60 * 60)); %>
                                                                            <% const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60)); %>
                                                                            <%= hours %>h <%= minutes %>m remaining
                                                                        <% } else { %>
                                                                            Expired
                                                                        <% } %>
                                                                    </small>
                                                                    <% } else { %>
                                                                    <span class="text-muted">Indefinite</span>
                                                                    <% } %>
                                                                </td>
                                                                <td>
                                                                    <small><%= member.suspensionReason || 'No reason provided' %></small>
                                                                </td>
                                                                <td>
                                                                    <button class="btn btn-sm btn-success"
                                                                            onclick="unsuspendMember('<%= member._id %>', '<%= member.username %>')"
                                                                            title="Unsuspend">
                                                                        <i class="fas fa-play"></i> Unsuspend
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                            <% }); %>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <% } %>
                                    </div>

                                    <!-- Recent Activity -->
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5><i class="fas fa-history"></i> Recent Activity</h5>
                                            </div>
                                            <div class="card-body">
                                                <% if (recentLogs.length === 0) { %>
                                                <p class="text-muted">No recent activity.</p>
                                                <% } else { %>
                                                <div style="max-height: 400px; overflow-y: auto;">
                                                    <% recentLogs.forEach(log => { %>
                                                    <div class="log-entry">
                                                        <small class="text-muted">
                                                            <%= new Date(log.createdAt).toLocaleString() %>
                                                        </small>
                                                        <div>
                                                            <strong><%= log.action %></strong>
                                                            <br>
                                                            <small><%= log.formatForDisplay() %></small>
                                                        </div>
                                                    </div>
                                                    <% }); %>
                                                </div>
                                                <% } %>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer class="app-footer">
            <ul class="list-inline">
                <li class="list-inline-item">
                    <a class="text-muted" href="#" target="_blank">Support</a>
                </li>
            </ul>
            <div class="copyright">Nexoria Staff Dashboard</div>
        </footer>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="../home/<USER>/vendor/popper-js/umd/popper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="../home/<USER>/javascript/theme.min.js"></script>
    <!-- Promote Modal -->
    <div class="modal fade" id="promoteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Promote Staff Member</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Promote <strong id="promoteUsername"></strong> to:</p>
                    <select class="form-control" id="promoteRankSelect">
                        <% ranks.forEach(rank => { %>
                        <option value="<%= rank._id %>"><%= rank.name %> (Level <%= rank.level %>)</option>
                        <% }); %>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="confirmPromote()">Promote</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Demote Modal -->
    <div class="modal fade" id="demoteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Demote Staff Member</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Demote <strong id="demoteUsername"></strong> to:</p>
                    <select class="form-control" id="demoteRankSelect">
                        <% ranks.forEach(rank => { %>
                        <option value="<%= rank._id %>"><%= rank.name %> (Level <%= rank.level %>)</option>
                        <% }); %>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" onclick="confirmDemote()">Demote</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Suspend Modal -->
    <div class="modal fade" id="suspendModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Suspend Staff Member</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Suspend <strong id="suspendUsername"></strong></p>
                    <div class="form-group">
                        <label for="suspendDuration">Duration:</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="suspendDuration" value="24" min="1" max="720">
                            <div class="input-group-append">
                                <select class="form-control" id="suspendDurationUnit">
                                    <option value="minutes">Minutes</option>
                                    <option value="hours" selected>Hours</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="suspendReason">Reason:</label>
                        <textarea class="form-control" id="suspendReason" rows="3" placeholder="Enter suspension reason..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" onclick="confirmSuspend()">Suspend</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentMemberId = null;
        let currentMemberUsername = null;
        let currentRankId = null;

        function changeGuild() {
            const guildId = document.getElementById('guildSelector').value;
            window.location.href = `/staff?guild=${guildId}`;
        }

        // Auto-refresh the page every 30 seconds to show new staff members
        setInterval(function() {
            // Only refresh if no modals are open
            if (!document.querySelector('.modal.show')) {
                console.log('[Dashboard] Auto-refreshing staff data...');
                location.reload();
            }
        }, 30000); // 30 seconds

        // Manual refresh button functionality
        function refreshStaffData() {
            console.log('[Dashboard] Manual refresh triggered');
            location.reload();
        }

        function showPromoteModal(memberId, username, rankId) {
            currentMemberId = memberId;
            currentMemberUsername = username;
            currentRankId = rankId;

            document.getElementById('promoteUsername').textContent = username;

            // Filter ranks to show only higher levels
            const select = document.getElementById('promoteRankSelect');
            const options = select.querySelectorAll('option');
            const ranksData = [
                <% ranks.forEach((rank, index) => { %>
                { _id: '<%= rank._id %>', name: '<%= rank.name %>', level: <%= rank.level %> }<%= index < ranks.length - 1 ? ',' : '' %>
                <% }); %>
            ];
            options.forEach(option => {
                const rank = ranksData.find(r => r._id === option.value);
                const currentRank = ranksData.find(r => r._id === rankId);
                option.style.display = rank && currentRank && rank.level > currentRank.level ? 'block' : 'none';
            });

            $('#promoteModal').modal('show');
        }

        function showDemoteModal(memberId, username, rankId) {
            currentMemberId = memberId;
            currentMemberUsername = username;
            currentRankId = rankId;

            document.getElementById('demoteUsername').textContent = username;

            // Filter ranks to show only lower levels
            const select = document.getElementById('demoteRankSelect');
            const options = select.querySelectorAll('option');
            options.forEach(option => {
                const rank = ranksData.find(r => r._id === option.value);
                const currentRank = ranksData.find(r => r._id === rankId);
                option.style.display = rank && currentRank && rank.level < currentRank.level ? 'block' : 'none';
            });

            $('#demoteModal').modal('show');
        }

        function showSuspendModal(memberId, username) {
            currentMemberId = memberId;
            currentMemberUsername = username;

            document.getElementById('suspendUsername').textContent = username;
            $('#suspendModal').modal('show');
        }

        function confirmPromote() {
            const newRankId = document.getElementById('promoteRankSelect').value;
            if (!newRankId) {
                alert('Please select a rank');
                return;
            }

            performAction('promote', { newRankId });
        }

        function confirmDemote() {
            const newRankId = document.getElementById('demoteRankSelect').value;
            if (!newRankId) {
                alert('Please select a rank');
                return;
            }

            performAction('demote', { newRankId });
        }

        function confirmSuspend() {
            console.log('[Dashboard] confirmSuspend called');
            const duration = document.getElementById('suspendDuration').value;
            const durationUnit = document.getElementById('suspendDurationUnit').value;
            const reason = document.getElementById('suspendReason').value;

            console.log('[Dashboard] Suspend data:', { duration, durationUnit, reason });

            if (!duration || duration < 1) {
                alert('Please enter a valid duration');
                return;
            }

            performAction('suspend', { duration: parseInt(duration), durationUnit, reason });
        }

        function unsuspendMember(memberId, username) {
            if (confirm(`Are you sure you want to unsuspend ${username}?`)) {
                currentMemberId = memberId;
                currentMemberUsername = username;
                performAction('unsuspend', {});
            }
        }

        function fireMember(memberId, username) {
            console.log('[Dashboard] fireMember called:', { memberId, username });
            if (confirm(`Are you sure you want to fire ${username}? This action cannot be undone.`)) {
                currentMemberId = memberId;
                currentMemberUsername = username;
                console.log('[Dashboard] Fire confirmed, calling performAction');
                performAction('fire', {});
            }
        }

        function performAction(action, data) {
            console.log(`[Dashboard] Performing action: ${action}`, {
                memberId: currentMemberId,
                guildId: '<%= selectedGuild ? selectedGuild.id : "" %>',
                ...data
            });

            fetch(`/staff/manage/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    memberId: currentMemberId,
                    guildId: '<%= selectedGuild ? selectedGuild.id : "" %>',
                    ...data
                })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(`Successfully ${action}d ${currentMemberUsername}!`);
                    location.reload();
                } else {
                    alert(`Error: ${result.message || `Failed to ${action} member`}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(`An error occurred while ${action}ing the member`);
            })
            .finally(() => {
                $('.modal').modal('hide');
            });
        }
    </script>
</body>
</html>
