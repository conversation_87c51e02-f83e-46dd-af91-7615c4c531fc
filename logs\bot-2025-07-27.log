2025-07-27 00:02:02 [INFO]: Loaded command: demote
2025-07-27 00:02:02 [INFO]: Loaded command: fire
2025-07-27 00:02:02 [INFO]: Loaded command: hire
2025-07-27 00:02:02 [INFO]: Loaded command: promote
2025-07-27 00:02:02 [INFO]: Loaded command: setup
2025-07-27 00:02:02 [INFO]: Loaded command: staff
2025-07-27 00:02:02 [INFO]: Loaded command: suspend
2025-07-27 00:02:02 [INFO]: Loaded command: unsuspend
2025-07-27 00:02:02 [INFO]: Loaded event: error
2025-07-27 00:02:02 [INFO]: Loaded event: guildCreate
2025-07-27 00:02:02 [INFO]: Loaded event: guildDelete
2025-07-27 00:02:02 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:02:02 [INFO]: Loaded event: interactionCreate
2025-07-27 00:02:02 [INFO]: Loaded event: ready
2025-07-27 00:02:02 [INFO]: Loaded event: warn
2025-07-27 00:02:02 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:02:03 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:02:03 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:02:03 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:02:04 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:02:04 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:02:04 [INFO]: Bot startup logged to Discord
2025-07-27 00:02:04 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.4147415},"timestamp":"2025-07-27T04:02:04.288Z"}
2025-07-27 00:02:04 [INFO]: Registering slash commands...
2025-07-27 00:02:04 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:02:04 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:05:21 [INFO]: Loaded command: demote
2025-07-27 00:05:21 [INFO]: Loaded command: fire
2025-07-27 00:05:21 [INFO]: Loaded command: hire
2025-07-27 00:05:21 [INFO]: Loaded command: promote
2025-07-27 00:05:21 [INFO]: Loaded command: setup
2025-07-27 00:05:21 [INFO]: Loaded command: staff
2025-07-27 00:05:21 [INFO]: Loaded command: suspend
2025-07-27 00:05:21 [INFO]: Loaded command: unsuspend
2025-07-27 00:05:21 [INFO]: Loaded event: error
2025-07-27 00:05:21 [INFO]: Loaded event: guildCreate
2025-07-27 00:05:21 [INFO]: Loaded event: guildDelete
2025-07-27 00:05:21 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:05:21 [INFO]: Loaded event: interactionCreate
2025-07-27 00:05:21 [INFO]: Loaded event: ready
2025-07-27 00:05:21 [INFO]: Loaded event: warn
2025-07-27 00:05:21 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:05:21 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:05:21 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:05:21 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:05:22 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:05:22 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:05:22 [INFO]: Bot startup logged to Discord
2025-07-27 00:05:22 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.4064248},"timestamp":"2025-07-27T04:05:22.470Z"}
2025-07-27 00:05:22 [INFO]: Registering slash commands...
2025-07-27 00:05:22 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:05:22 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:13:41 [INFO]: Loaded command: demote
2025-07-27 00:13:41 [INFO]: Loaded command: fire
2025-07-27 00:13:41 [INFO]: Loaded command: hire
2025-07-27 00:13:41 [INFO]: Loaded command: promote
2025-07-27 00:13:41 [INFO]: Loaded command: setup
2025-07-27 00:13:41 [INFO]: Loaded command: staff
2025-07-27 00:13:41 [INFO]: Loaded command: suspend
2025-07-27 00:13:41 [INFO]: Loaded command: unsuspend
2025-07-27 00:13:41 [INFO]: Loaded event: error
2025-07-27 00:13:41 [INFO]: Loaded event: guildCreate
2025-07-27 00:13:41 [INFO]: Loaded event: guildDelete
2025-07-27 00:13:41 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:13:41 [INFO]: Loaded event: interactionCreate
2025-07-27 00:13:41 [INFO]: Loaded event: ready
2025-07-27 00:13:41 [INFO]: Loaded event: warn
2025-07-27 00:13:41 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:13:41 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:13:42 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:13:42 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:13:42 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:13:42 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:13:42 [INFO]: Bot startup logged to Discord
2025-07-27 00:13:42 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.7110792},"timestamp":"2025-07-27T04:13:42.974Z"}
2025-07-27 00:13:42 [INFO]: Registering slash commands...
2025-07-27 00:13:43 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:13:43 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:16:56 [INFO]: Command unsuspend used by timmycas in 😊 Nexoria Development | Testing
2025-07-27 00:17:09 [INFO]: Command unsuspend used by timmycas in 😊 Nexoria Development | Testing
2025-07-27 00:22:30 [INFO]: Loaded command: demote
2025-07-27 00:22:30 [INFO]: Loaded command: fire
2025-07-27 00:22:30 [INFO]: Loaded command: hire
2025-07-27 00:22:30 [INFO]: Loaded command: promote
2025-07-27 00:22:30 [INFO]: Loaded command: setup
2025-07-27 00:22:30 [INFO]: Loaded command: staff
2025-07-27 00:22:30 [INFO]: Loaded command: suspend
2025-07-27 00:22:30 [INFO]: Loaded command: unsuspend
2025-07-27 00:22:30 [INFO]: Loaded event: error
2025-07-27 00:22:30 [INFO]: Loaded event: guildCreate
2025-07-27 00:22:30 [INFO]: Loaded event: guildDelete
2025-07-27 00:22:30 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:22:30 [INFO]: Loaded event: interactionCreate
2025-07-27 00:22:30 [INFO]: Loaded event: ready
2025-07-27 00:22:30 [INFO]: Loaded event: warn
2025-07-27 00:22:30 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:22:30 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:22:30 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:22:30 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:22:31 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:22:31 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:22:31 [INFO]: Bot startup logged to Discord
2025-07-27 00:22:31 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.9694787},"timestamp":"2025-07-27T04:22:31.462Z"}
2025-07-27 00:22:31 [INFO]: Registering slash commands...
2025-07-27 00:22:31 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:22:31 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:25:29 [INFO]: Loaded command: demote
2025-07-27 00:25:29 [INFO]: Loaded command: fire
2025-07-27 00:25:29 [INFO]: Loaded command: hire
2025-07-27 00:25:29 [INFO]: Loaded command: promote
2025-07-27 00:25:29 [INFO]: Loaded command: setup
2025-07-27 00:25:29 [INFO]: Loaded command: staff
2025-07-27 00:25:29 [INFO]: Loaded command: suspend
2025-07-27 00:25:29 [INFO]: Loaded command: unsuspend
2025-07-27 00:25:29 [INFO]: Loaded event: error
2025-07-27 00:25:29 [INFO]: Loaded event: guildCreate
2025-07-27 00:25:29 [INFO]: Loaded event: guildDelete
2025-07-27 00:25:29 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:25:29 [INFO]: Loaded event: interactionCreate
2025-07-27 00:25:29 [INFO]: Loaded event: ready
2025-07-27 00:25:29 [INFO]: Loaded event: warn
2025-07-27 00:25:29 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:25:29 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:25:30 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:25:30 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:25:30 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:25:30 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:25:30 [INFO]: Bot startup logged to Discord
2025-07-27 00:25:30 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.2946137},"timestamp":"2025-07-27T04:25:30.622Z"}
2025-07-27 00:25:30 [INFO]: Registering slash commands...
2025-07-27 00:25:30 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:25:30 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:28:29 [INFO]: Loaded command: demote
2025-07-27 00:28:29 [INFO]: Loaded command: fire
2025-07-27 00:28:29 [INFO]: Loaded command: hire
2025-07-27 00:28:29 [INFO]: Loaded command: promote
2025-07-27 00:28:29 [INFO]: Loaded command: setup
2025-07-27 00:28:29 [INFO]: Loaded command: staff
2025-07-27 00:28:29 [INFO]: Loaded command: suspend
2025-07-27 00:28:29 [INFO]: Loaded command: unsuspend
2025-07-27 00:28:29 [INFO]: Loaded event: error
2025-07-27 00:28:29 [INFO]: Loaded event: guildCreate
2025-07-27 00:28:29 [INFO]: Loaded event: guildDelete
2025-07-27 00:28:29 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:28:29 [INFO]: Loaded event: interactionCreate
2025-07-27 00:28:29 [INFO]: Loaded event: ready
2025-07-27 00:28:29 [INFO]: Loaded event: warn
2025-07-27 00:28:29 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:28:29 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:28:30 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:28:30 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:28:30 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:28:30 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:28:30 [INFO]: Bot startup logged to Discord
2025-07-27 00:28:30 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.2290199},"timestamp":"2025-07-27T04:28:30.901Z"}
2025-07-27 00:28:30 [INFO]: Registering slash commands...
2025-07-27 00:28:31 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:28:31 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:29:13 [INFO]: Command hire used by timmycas in 😊 Nexoria Development | Testing
2025-07-27 00:32:56 [INFO]: Loaded command: demote
2025-07-27 00:32:56 [INFO]: Loaded command: fire
2025-07-27 00:32:56 [INFO]: Loaded command: hire
2025-07-27 00:32:56 [INFO]: Loaded command: promote
2025-07-27 00:32:56 [INFO]: Loaded command: setup
2025-07-27 00:32:56 [INFO]: Loaded command: staff
2025-07-27 00:32:56 [INFO]: Loaded command: suspend
2025-07-27 00:32:56 [INFO]: Loaded command: unsuspend
2025-07-27 00:32:56 [INFO]: Loaded event: error
2025-07-27 00:32:56 [INFO]: Loaded event: guildCreate
2025-07-27 00:32:56 [INFO]: Loaded event: guildDelete
2025-07-27 00:32:56 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:32:56 [INFO]: Loaded event: interactionCreate
2025-07-27 00:32:56 [INFO]: Loaded event: ready
2025-07-27 00:32:56 [INFO]: Loaded event: warn
2025-07-27 00:32:56 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:32:56 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:32:57 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:32:57 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:32:57 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:32:57 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:32:57 [INFO]: Bot startup logged to Discord
2025-07-27 00:32:57 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.2643217},"timestamp":"2025-07-27T04:32:57.919Z"}
2025-07-27 00:32:57 [INFO]: Registering slash commands...
2025-07-27 00:32:58 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:32:58 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:36:15 [INFO]: Loaded command: demote
2025-07-27 00:36:15 [INFO]: Loaded command: fire
2025-07-27 00:36:15 [INFO]: Loaded command: hire
2025-07-27 00:36:15 [INFO]: Loaded command: promote
2025-07-27 00:36:15 [INFO]: Loaded command: setup
2025-07-27 00:36:15 [INFO]: Loaded command: staff
2025-07-27 00:36:15 [INFO]: Loaded command: suspend
2025-07-27 00:36:15 [INFO]: Loaded command: unsuspend
2025-07-27 00:36:15 [INFO]: Loaded event: error
2025-07-27 00:36:15 [INFO]: Loaded event: guildCreate
2025-07-27 00:36:15 [INFO]: Loaded event: guildDelete
2025-07-27 00:36:15 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:36:15 [INFO]: Loaded event: interactionCreate
2025-07-27 00:36:15 [INFO]: Loaded event: ready
2025-07-27 00:36:15 [INFO]: Loaded event: warn
2025-07-27 00:36:15 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:36:16 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:36:16 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:36:16 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:36:17 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:36:17 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:36:17 [INFO]: Bot startup logged to Discord
2025-07-27 00:36:17 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.4189449},"timestamp":"2025-07-27T04:36:17.300Z"}
2025-07-27 00:36:17 [INFO]: Registering slash commands...
2025-07-27 00:36:17 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:36:17 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:36:53 [INFO]: Command hire used by timmycas in 😊 Nexoria Development | Testing
2025-07-27 00:39:10 [INFO]: Loaded command: demote
2025-07-27 00:39:10 [INFO]: Loaded command: fire
2025-07-27 00:39:10 [INFO]: Loaded command: hire
2025-07-27 00:39:10 [INFO]: Loaded command: promote
2025-07-27 00:39:10 [INFO]: Loaded command: setup
2025-07-27 00:39:10 [INFO]: Loaded command: staff
2025-07-27 00:39:10 [INFO]: Loaded command: suspend
2025-07-27 00:39:10 [INFO]: Loaded command: unsuspend
2025-07-27 00:39:10 [INFO]: Loaded event: error
2025-07-27 00:39:10 [INFO]: Loaded event: guildCreate
2025-07-27 00:39:10 [INFO]: Loaded event: guildDelete
2025-07-27 00:39:10 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:39:10 [INFO]: Loaded event: interactionCreate
2025-07-27 00:39:10 [INFO]: Loaded event: ready
2025-07-27 00:39:10 [INFO]: Loaded event: warn
2025-07-27 00:39:10 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:39:10 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:39:11 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:39:11 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:39:12 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:39:12 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:39:12 [INFO]: Bot startup logged to Discord
2025-07-27 00:39:12 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":4.0156813},"timestamp":"2025-07-27T04:39:12.473Z"}
2025-07-27 00:39:12 [INFO]: Registering slash commands...
2025-07-27 00:39:12 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:39:12 [INFO]: [36m🎉 Bot initialization complete![39m
2025-07-27 00:40:25 [INFO]: Command hire used by timmycas in 😊 Nexoria Development | Testing
2025-07-27 00:42:00 [INFO]: Found 1 expired suspension(s) to process
2025-07-27 00:42:01 [INFO]: Removed suspended role from recondev20#0
2025-07-27 00:42:01 [INFO]: Restored 3 role(s) to recondev20#0
2025-07-27 00:42:01 [INFO]: Updated suspension record for recondev20#0: Automatic unsuspension - suspension period expired
2025-07-27 00:42:01 [ERROR]: TypeError: Cannot read properties of null (reading 'getTime')
    at SuspensionManager.unsuspendMember (C:\Users\<USER>\Documents\GitHub\Nexoria-Staff\utils\suspensionManager.js:168:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SuspensionManager.checkExpiredSuspensions (C:\Users\<USER>\Documents\GitHub\Nexoria-Staff\utils\suspensionManager.js:69:17)
    at async cronJob.cron.schedule.scheduled [as _execution] (C:\Users\<USER>\Documents\GitHub\Nexoria-Staff\utils\suspensionManager.js:23:13)
2025-07-27 00:46:30 [INFO]: Loaded command: demote
2025-07-27 00:46:30 [INFO]: Loaded command: fire
2025-07-27 00:46:30 [INFO]: Loaded command: hire
2025-07-27 00:46:30 [INFO]: Loaded command: promote
2025-07-27 00:46:30 [INFO]: Loaded command: setup
2025-07-27 00:46:30 [INFO]: Loaded command: staff
2025-07-27 00:46:30 [INFO]: Loaded command: suspend
2025-07-27 00:46:30 [INFO]: Loaded command: unsuspend
2025-07-27 00:46:30 [INFO]: Loaded event: error
2025-07-27 00:46:30 [INFO]: Loaded event: guildCreate
2025-07-27 00:46:30 [INFO]: Loaded event: guildDelete
2025-07-27 00:46:30 [INFO]: Loaded event: guildMemberUpdate
2025-07-27 00:46:30 [INFO]: Loaded event: interactionCreate
2025-07-27 00:46:30 [INFO]: Loaded event: ready
2025-07-27 00:46:30 [INFO]: Loaded event: warn
2025-07-27 00:46:30 [INFO]: [36m🚀 Starting Nexoria Staff Bot...[39m
2025-07-27 00:46:30 [INFO]: [32m✅ Connected to MongoDB[39m
2025-07-27 00:46:31 [INFO]: [32m✅ Bot is ready! Logged in as Nexoria-Staff#8889[39m
2025-07-27 00:46:31 [INFO]: [34m📊 Serving 1 guilds with 1 users[39m
2025-07-27 00:46:31 [INFO]: [34m🌐 Dashboard server initialized[39m
2025-07-27 00:46:31 [INFO]: Suspension manager started - checking for expired suspensions every minute
2025-07-27 00:46:31 [INFO]: Bot startup logged to Discord
2025-07-27 00:46:31 [INFO]: Bot Event: {"event":"BOT_STARTUP","details":{"botTag":"Nexoria-Staff#8889","botId":"1394486493250191553","guildCount":1,"userCount":1,"nodeVersion":"v22.16.0","uptime":3.4582275},"timestamp":"2025-07-27T04:46:31.682Z"}
2025-07-27 00:46:31 [INFO]: Registering slash commands...
2025-07-27 00:46:31 [INFO]: Registered 8 commands to guild: 😊 Nexoria Development | Testing
2025-07-27 00:46:31 [INFO]: [36m🎉 Bot initialization complete![39m
